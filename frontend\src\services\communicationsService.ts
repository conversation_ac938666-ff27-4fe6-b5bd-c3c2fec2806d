import { BaseApiService } from '../shared/services/BaseApiService';
import type { BaseEntity } from '../shared/types';

export interface Notification extends BaseEntity {
  recipient: number;
  sender?: number;
  title: string;
  message: string;
  notification_type: string;
  is_read: boolean;
  is_urgent: boolean;
  reference_type?: string;
  reference_id?: string;
  email_sent: boolean;
  sms_sent: boolean;
  push_sent: boolean;
  read_at?: string;
}

export interface Message extends BaseEntity {
  sender?: number;
  recipient: number;
  subject: string;
  body: string;
  is_read: boolean;
  is_urgent: boolean;
  is_archived: boolean;
  parent_message?: number;
  attachment?: File | string;
  sent_at?: string;
  read_at?: string;
}

export interface Announcement extends BaseEntity {
  created_by?: number;
  title: string;
  content: string;
  target_roles: string;
  target_departments?: string;
  is_urgent: boolean;
  is_active: boolean;
  publish_date: string;
  expiry_date?: string;
}

export interface EmailTemplate extends BaseEntity {
  name: string;
  subject: string;
  body: string;
  template_type: string;
  is_active: boolean;
  send_copy_to_admin: boolean;
}

export interface SMSTemplate extends BaseEntity {
  name: string;
  message: string;
  template_type: string;
  is_active: boolean;
}

export interface CommunicationLog extends BaseEntity {
  recipient: number;
  communication_type: string;
  subject?: string;
  content: string;
  status: string;
  reference_type?: string;
  reference_id?: string;
  sent_at?: string;
  delivered_at?: string;
  error_message?: string;
}

export interface BulkNotification {
  title: string;
  message: string;
  notification_type: string;
  is_urgent: boolean;
  target_roles?: string[];
  target_users?: number[];
  send_email: boolean;
  send_sms: boolean;
  send_push: boolean;
}

export interface BulkMessage {
  subject: string;
  body: string;
  is_urgent: boolean;
  target_roles?: string[];
  target_users?: number[];
  attachment?: File;
}

class CommunicationsService extends BaseApiService<Notification> {
  constructor() {
    super({
      baseURL: '/communications',
      endpoints: {
        list: '/notifications/',
        detail: '/notifications/:id/',
        create: '/notifications/',
        update: '/notifications/:id/',
        delete: '/notifications/:id/',
      },
    });
  }

  // Notifications - using inherited CRUD methods
  async getNotifications(params?: any) {
    return this.getAll(params);
  }

  async getNotification(id: number) {
    return this.getById(id);
  }

  async createNotification(data: Notification) {
    return this.create(data);
  }

  async updateNotification(id: number, data: Partial<Notification>) {
    return this.update(id, data);
  }

  async deleteNotification(id: number) {
    return this.deleteById(id);
  }

  async getUnreadNotifications() {
    return this.get('/notifications/unread/');
  }

  async getUrgentNotifications() {
    return this.get('/notifications/urgent/');
  }

  async markNotificationRead(id: number) {
    return this.customAction(id, 'mark_read');
  }

  async markAllNotificationsRead() {
    return this.post('/notifications/mark_all_read/', {});
  }

  async getNotificationCounts() {
    return this.get('/notifications/count/');
  }

  async sendBulkNotifications(data: BulkNotification) {
    return this.post('/notifications/bulk_send/', data);
  }

  // Messages - using base service methods
  async getMessages(params?: any) {
    return this.get('/messages/', { params });
  }

  async getMessage(id: number) {
    return this.get('/messages/:id/', { params: { id } });
  }

  async createMessage(data: Message) {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'attachment' && value instanceof File) {
          formData.append(key, value);
        } else {
          formData.append(key, value.toString());
        }
      }
    });
    return this.post('/messages/', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }

  async updateMessage(id: number, data: Partial<Message>) {
    return this.patch('/messages/:id/', data, { params: { id } });
  }

  async deleteMessage(id: number) {
    return this.delete('/messages/:id/', { params: { id } });
  }

  async getInboxMessages() {
    return this.api.get('/communications/messages/inbox/');
  }

  async getSentMessages() {
    return this.api.get('/communications/messages/sent/');
  }

  async getUnreadMessages() {
    return this.api.get('/communications/messages/unread/');
  }

  async markMessageRead(id: number) {
    return this.api.post(`/communications/messages/${id}/mark_read/`);
  }

  async archiveMessage(id: number) {
    return this.api.post(`/communications/messages/${id}/archive/`);
  }

  async replyToMessage(id: number, data: { body: string; attachment?: File }) {
    const formData = new FormData();
    formData.append('body', data.body);
    if (data.attachment) {
      formData.append('attachment', data.attachment);
    }
    return this.api.post(`/communications/messages/${id}/reply/`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }

  async sendBulkMessages(data: BulkMessage) {
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'attachment' && value instanceof File) {
          formData.append(key, value);
        } else if (Array.isArray(value)) {
          value.forEach((item, index) => {
            formData.append(`${key}[${index}]`, item.toString());
          });
        } else {
          formData.append(key, value.toString());
        }
      }
    });
    return this.api.post('/communications/messages/bulk_send/', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }

  // Announcements
  async getAnnouncements(params?: any) {
    return this.api.get('/communications/announcements/', { params });
  }

  async getAnnouncement(id: number) {
    return this.api.get(`/communications/announcements/${id}/`);
  }

  async createAnnouncement(data: Announcement) {
    return this.api.post('/communications/announcements/', data);
  }

  async updateAnnouncement(id: number, data: Partial<Announcement>) {
    return this.api.patch(`/communications/announcements/${id}/`, data);
  }

  async deleteAnnouncement(id: number) {
    return this.api.delete(`/communications/announcements/${id}/`);
  }

  async getActiveAnnouncements() {
    return this.api.get('/communications/announcements/active/');
  }

  async getUrgentAnnouncements() {
    return this.api.get('/communications/announcements/urgent/');
  }

  // Email Templates
  async getEmailTemplates(params?: any) {
    return this.api.get('/communications/email-templates/', { params });
  }

  async getEmailTemplate(id: number) {
    return this.api.get(`/communications/email-templates/${id}/`);
  }

  async createEmailTemplate(data: EmailTemplate) {
    return this.api.post('/communications/email-templates/', data);
  }

  async updateEmailTemplate(id: number, data: Partial<EmailTemplate>) {
    return this.api.patch(`/communications/email-templates/${id}/`, data);
  }

  async deleteEmailTemplate(id: number) {
    return this.api.delete(`/communications/email-templates/${id}/`);
  }

  async getActiveEmailTemplates() {
    return this.api.get('/communications/email-templates/active/');
  }

  async getEmailTemplatesByType(type?: string) {
    return this.api.get('/communications/email-templates/by_type/', { params: { type } });
  }

  // SMS Templates
  async getSMSTemplates(params?: any) {
    return this.api.get('/communications/sms-templates/', { params });
  }

  async getSMSTemplate(id: number) {
    return this.api.get(`/communications/sms-templates/${id}/`);
  }

  async createSMSTemplate(data: SMSTemplate) {
    return this.api.post('/communications/sms-templates/', data);
  }

  async updateSMSTemplate(id: number, data: Partial<SMSTemplate>) {
    return this.api.patch(`/communications/sms-templates/${id}/`, data);
  }

  async deleteSMSTemplate(id: number) {
    return this.api.delete(`/communications/sms-templates/${id}/`);
  }

  async getActiveSMSTemplates() {
    return this.api.get('/communications/sms-templates/active/');
  }

  async getSMSTemplatesByType(type?: string) {
    return this.api.get('/communications/sms-templates/by_type/', { params: { type } });
  }

  // Communication Logs
  async getCommunicationLogs(params?: any) {
    return this.api.get('/communications/communication-logs/', { params });
  }

  async getCommunicationLog(id: number) {
    return this.api.get(`/communications/communication-logs/${id}/`);
  }

  async createCommunicationLog(data: CommunicationLog) {
    return this.api.post('/communications/communication-logs/', data);
  }

  async getFailedCommunications() {
    return this.api.get('/communications/communication-logs/failed/');
  }

  async getCommunicationStatistics() {
    return this.api.get('/communications/communication-logs/statistics/');
  }
}

export const communicationsService = new CommunicationsService();
