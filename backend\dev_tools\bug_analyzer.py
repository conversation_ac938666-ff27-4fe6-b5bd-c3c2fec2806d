#!/usr/bin/env python3
"""
HMS Bug Analyzer
Analyzes bugs while preserving integration fixes
"""

import os
import sys
import django
import traceback
from pathlib import Path

# Add parent directory to path for Django imports
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import connection
from rest_framework.test import APIClient

User = get_user_model()

class HMSBugAnalyzer:
    """Analyze bugs while preserving HMS integration standards"""
    
    def __init__(self):
        self.client = APIClient()
        self.issues_found = []
        
    def check_integration_health(self):
        """Check if our integration fixes are still intact"""
        print("🔍 Checking Integration Health...")
        
        issues = []
        
        # Check 1: Token storage consistency
        frontend_files = [
            'frontend/src/utils/api.ts',
            'frontend/src/store/slices/authSlice.ts',
            'frontend/src/services/aiService.ts'
        ]
        
        for file_path in frontend_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "localStorage.getItem('access_token')" in content:
                        issues.append(f"❌ {file_path} uses inconsistent token storage")
                    elif "localStorage.getItem('token')" in content:
                        print(f"✅ {file_path} uses correct token storage")
        
        # Check 2: Patient ID format
        try:
            from patient_management.models import Patient
            patients = Patient.objects.all()
            invalid_ids = []
            
            for patient in patients:
                if not patient.patient_id.startswith('P') or len(patient.patient_id) != 7:
                    invalid_ids.append(patient.patient_id)
            
            if invalid_ids:
                issues.append(f"❌ Invalid patient IDs found: {invalid_ids}")
            else:
                print(f"✅ All {patients.count()} patient IDs are valid")
                
        except Exception as e:
            issues.append(f"❌ Patient ID check failed: {e}")
        
        # Check 3: Model duplicates
        try:
            from users.models import DoctorProfile, NurseProfile
            print(f"✅ DoctorProfile model accessible")
            print(f"✅ NurseProfile model accessible")
        except Exception as e:
            issues.append(f"❌ Model import issue: {e}")
        
        # Check 4: API endpoints
        try:
            # Create test user
            test_user = User.objects.create_user(
                username='bug_test_user',
                email='<EMAIL>',
                password='testpass123',
                role='admin'
            )
            
            # Test authentication
            response = self.client.post('/api/auth/login/', {
                'username': 'bug_test_user',
                'password': 'testpass123'
            })
            
            if response.status_code == 200:
                print("✅ Authentication endpoint working")
                
                # Test API endpoints
                auth_data = response.json()
                if 'tokens' in auth_data:
                    token = auth_data['tokens']['access']
                    self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
                    
                    endpoints = [
                        '/api/users/users/',
                        '/api/patients/patients/',
                        '/api/appointments/appointments/'
                    ]
                    
                    for endpoint in endpoints:
                        resp = self.client.get(endpoint)
                        if resp.status_code == 200:
                            print(f"✅ {endpoint} working")
                        else:
                            issues.append(f"❌ {endpoint} returned {resp.status_code}")
                else:
                    issues.append("❌ Login response missing tokens")
            else:
                issues.append(f"❌ Authentication failed: {response.status_code}")
            
            # Cleanup
            test_user.delete()
            
        except Exception as e:
            issues.append(f"❌ API test failed: {e}")
        
        self.issues_found.extend(issues)
        return len(issues) == 0
    
    def analyze_database_consistency(self):
        """Analyze database for consistency issues"""
        print("\n🔍 Analyzing Database Consistency...")
        
        issues = []
        
        try:
            # Check for orphaned records
            with connection.cursor() as cursor:
                # Check for users without profiles where expected
                cursor.execute("""
                    SELECT COUNT(*) FROM users_user u 
                    WHERE u.role = 'doctor' 
                    AND NOT EXISTS (
                        SELECT 1 FROM users_doctorprofile dp 
                        WHERE dp.user_id = u.id
                    )
                """)
                orphaned_doctors = cursor.fetchone()[0]
                
                if orphaned_doctors > 0:
                    issues.append(f"❌ {orphaned_doctors} doctors without profiles")
                else:
                    print("✅ All doctors have profiles")
                
                # Check for invalid foreign keys
                cursor.execute("""
                    SELECT COUNT(*) FROM appointments a
                    WHERE NOT EXISTS (
                        SELECT 1 FROM patient_management_patient p
                        WHERE p.id = a.patient_id
                    )
                """)
                invalid_appointments = cursor.fetchone()[0]
                
                if invalid_appointments > 0:
                    issues.append(f"❌ {invalid_appointments} appointments with invalid patient references")
                else:
                    print("✅ All appointments have valid patient references")
                    
        except Exception as e:
            issues.append(f"❌ Database consistency check failed: {e}")
        
        self.issues_found.extend(issues)
        return len(issues) == 0
    
    def analyze_api_performance(self):
        """Analyze API performance issues"""
        print("\n🔍 Analyzing API Performance...")
        
        issues = []
        
        try:
            import time
            from django.test.utils import override_settings
            
            # Create test user
            test_user = User.objects.create_user(
                username='perf_test_user',
                email='<EMAIL>',
                password='testpass123',
                role='admin'
            )
            
            # Get token
            response = self.client.post('/api/auth/login/', {
                'username': 'perf_test_user',
                'password': 'testpass123'
            })
            
            if response.status_code == 200:
                auth_data = response.json()
                token = auth_data['tokens']['access']
                self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
                
                # Test endpoint performance
                endpoints = [
                    '/api/users/users/',
                    '/api/patients/patients/',
                    '/api/appointments/appointments/'
                ]
                
                for endpoint in endpoints:
                    start_time = time.time()
                    resp = self.client.get(endpoint)
                    end_time = time.time()
                    
                    response_time = end_time - start_time
                    
                    if response_time > 2.0:  # 2 second threshold
                        issues.append(f"❌ {endpoint} slow response: {response_time:.2f}s")
                    else:
                        print(f"✅ {endpoint} response time: {response_time:.2f}s")
            
            # Cleanup
            test_user.delete()
            
        except Exception as e:
            issues.append(f"❌ Performance analysis failed: {e}")
        
        self.issues_found.extend(issues)
        return len(issues) == 0
    
    def check_frontend_errors(self):
        """Check for common frontend issues"""
        print("\n🔍 Checking Frontend Issues...")
        
        issues = []
        
        # Check for common TypeScript issues
        frontend_dir = Path('frontend/src')
        if frontend_dir.exists():
            # Check for any/unknown types
            ts_files = list(frontend_dir.rglob('*.ts')) + list(frontend_dir.rglob('*.tsx'))
            
            for file_path in ts_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        # Check for any types
                        if ': any' in content or 'any[]' in content:
                            issues.append(f"⚠️  {file_path} contains 'any' types")
                        
                        # Check for console.log (should be removed in production)
                        if 'console.log(' in content:
                            issues.append(f"⚠️  {file_path} contains console.log statements")
                            
                except Exception as e:
                    issues.append(f"❌ Error reading {file_path}: {e}")
        
        self.issues_found.extend(issues)
        return len(issues) == 0
    
    def generate_bug_report(self, bug_description: str = None):
        """Generate comprehensive bug report"""
        print("\n📋 GENERATING BUG ANALYSIS REPORT")
        print("=" * 50)
        
        if bug_description:
            print(f"Bug Description: {bug_description}")
            print("-" * 50)
        
        # Run all checks
        integration_ok = self.check_integration_health()
        database_ok = self.analyze_database_consistency()
        performance_ok = self.analyze_api_performance()
        frontend_ok = self.check_frontend_errors()
        
        print("\n📊 ANALYSIS SUMMARY")
        print("=" * 50)
        
        status_map = {
            integration_ok: "Integration Health",
            database_ok: "Database Consistency", 
            performance_ok: "API Performance",
            frontend_ok: "Frontend Code Quality"
        }
        
        for status, name in status_map.items():
            icon = "✅" if status else "❌"
            print(f"{icon} {name}")
        
        if self.issues_found:
            print(f"\n🚨 ISSUES FOUND ({len(self.issues_found)})")
            print("-" * 50)
            for issue in self.issues_found:
                print(f"  {issue}")
        else:
            print("\n🎉 NO ISSUES FOUND!")
            print("Your HMS application is healthy!")
        
        print(f"\n🔧 RECOMMENDATIONS")
        print("-" * 50)
        
        if not integration_ok:
            print("1. Run integration fix script to restore standards")
        if not database_ok:
            print("2. Run database consistency repair")
        if not performance_ok:
            print("3. Optimize slow API endpoints")
        if not frontend_ok:
            print("4. Clean up frontend code quality issues")
        
        if all([integration_ok, database_ok, performance_ok, frontend_ok]):
            print("✅ All systems healthy - ready for development!")
        
        return len(self.issues_found) == 0


def main():
    """Main bug analysis function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='HMS Bug Analyzer')
    parser.add_argument('--description', '-d', help='Bug description')
    parser.add_argument('--quick', '-q', action='store_true', help='Quick analysis only')
    
    args = parser.parse_args()
    
    analyzer = HMSBugAnalyzer()
    
    if args.quick:
        # Quick integration health check only
        analyzer.check_integration_health()
    else:
        # Full analysis
        analyzer.generate_bug_report(args.description)


if __name__ == '__main__':
    main()
