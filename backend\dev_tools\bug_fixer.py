#!/usr/bin/env python3
"""
HMS Bug Fixer - Comprehensive Bug Fixing Assistant
Maintains integration standards while fixing issues
"""

import os
import sys
import django
import json
import traceback
from pathlib import Path
from datetime import datetime

# Add parent directory to path for Django imports
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import connection
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

class HMSBugFixer:
    """Comprehensive bug fixing assistant for HMS"""
    
    def __init__(self):
        self.client = APIClient()
        self.bug_report = {}
        self.analysis_results = {}
        self.fix_recommendations = []
        
    def analyze_bug(self, bug_description: str, error_message: str = None, 
                   component: str = None, steps_to_reproduce: list = None):
        """Comprehensive bug analysis"""
        
        print("🔍 ANALYZING BUG")
        print("=" * 50)
        print(f"Description: {bug_description}")
        if error_message:
            print(f"Error: {error_message}")
        if component:
            print(f"Component: {component}")
        print()
        
        self.bug_report = {
            'description': bug_description,
            'error_message': error_message,
            'component': component,
            'steps_to_reproduce': steps_to_reproduce or [],
            'timestamp': datetime.now().isoformat(),
            'analysis_results': {}
        }
        
        # Run comprehensive analysis
        self._check_integration_health()
        self._analyze_authentication_issues()
        self._analyze_api_issues()
        self._analyze_database_issues()
        self._analyze_frontend_issues()
        
        return self.analysis_results
    
    def _check_integration_health(self):
        """Check if integration standards are intact"""
        print("🛡️ Checking Integration Health...")
        
        issues = []
        
        # Check token storage consistency
        frontend_files = [
            'frontend/src/utils/api.ts',
            'frontend/src/store/slices/authSlice.ts',
            'frontend/src/services/aiService.ts'
        ]
        
        for file_path in frontend_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "localStorage.getItem('access_token')" in content:
                        issues.append(f"❌ {file_path} uses inconsistent token storage")
                    elif "localStorage.getItem('token')" in content:
                        print(f"✅ {file_path} token storage correct")
        
        # Check patient ID format
        try:
            from patient_management.models import Patient
            patients = Patient.objects.all()
            invalid_ids = []
            
            for patient in patients:
                if not patient.patient_id.startswith('P') or len(patient.patient_id) != 7:
                    invalid_ids.append(patient.patient_id)
            
            if invalid_ids:
                issues.append(f"❌ Invalid patient IDs: {invalid_ids}")
            else:
                print(f"✅ All {patients.count()} patient IDs valid")
                
        except Exception as e:
            issues.append(f"❌ Patient ID check failed: {e}")
        
        self.analysis_results['integration_health'] = {
            'status': 'healthy' if not issues else 'issues_found',
            'issues': issues
        }
        
        return len(issues) == 0
    
    def _analyze_authentication_issues(self):
        """Analyze authentication-related issues"""
        print("\n🔐 Analyzing Authentication...")
        
        issues = []
        
        try:
            # Test authentication flow
            test_user = User.objects.create_user(
                username='bug_auth_test',
                email='<EMAIL>',
                password='testpass123',
                role='admin'
            )
            
            # Test login
            response = self.client.post('/api/auth/login/', {
                'username': 'bug_auth_test',
                'password': 'testpass123'
            })
            
            if response.status_code == 200:
                auth_data = response.json()
                if 'tokens' in auth_data and 'access' in auth_data['tokens']:
                    print("✅ Authentication flow working")
                    
                    # Test authenticated request
                    token = auth_data['tokens']['access']
                    self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
                    
                    profile_response = self.client.get('/api/auth/profile/')
                    if profile_response.status_code == 200:
                        print("✅ Authenticated requests working")
                    else:
                        issues.append(f"❌ Profile request failed: {profile_response.status_code}")
                else:
                    issues.append("❌ Invalid login response format")
            else:
                issues.append(f"❌ Login failed: {response.status_code}")
                if response.content:
                    issues.append(f"   Response: {response.content.decode()}")
            
            # Cleanup
            test_user.delete()
            
        except Exception as e:
            issues.append(f"❌ Authentication test failed: {e}")
        
        self.analysis_results['authentication'] = {
            'status': 'working' if not issues else 'issues_found',
            'issues': issues
        }
        
        return len(issues) == 0
    
    def _analyze_api_issues(self):
        """Analyze API endpoint issues"""
        print("\n📡 Analyzing API Endpoints...")
        
        issues = []
        
        try:
            # Create test user for API testing
            test_user = User.objects.create_user(
                username='bug_api_test',
                email='<EMAIL>',
                password='testpass123',
                role='admin'
            )
            
            # Get token
            refresh = RefreshToken.for_user(test_user)
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
            
            # Test core endpoints
            endpoints = [
                ('/api/', 'API Root'),
                ('/api/users/users/', 'Users List'),
                ('/api/patients/patients/', 'Patients List'),
                ('/api/appointments/appointments/', 'Appointments List'),
            ]
            
            for endpoint, name in endpoints:
                try:
                    response = self.client.get(endpoint)
                    if response.status_code == 200:
                        print(f"✅ {name} working")
                        
                        # Check response format for list endpoints
                        if endpoint != '/api/':
                            data = response.json()
                            if 'results' not in data:
                                issues.append(f"❌ {name} missing 'results' field")
                            else:
                                print(f"   Response format correct")
                    else:
                        issues.append(f"❌ {name}: Status {response.status_code}")
                        
                except Exception as e:
                    issues.append(f"❌ {name}: Error {e}")
            
            # Cleanup
            test_user.delete()
            
        except Exception as e:
            issues.append(f"❌ API test setup failed: {e}")
        
        self.analysis_results['api_endpoints'] = {
            'status': 'working' if not issues else 'issues_found',
            'issues': issues
        }
        
        return len(issues) == 0
    
    def _analyze_database_issues(self):
        """Analyze database consistency issues"""
        print("\n🗄️ Analyzing Database...")
        
        issues = []
        
        try:
            # Check for orphaned records
            with connection.cursor() as cursor:
                # Check appointments with invalid patient references
                cursor.execute("""
                    SELECT COUNT(*) FROM appointments a
                    WHERE NOT EXISTS (
                        SELECT 1 FROM patient_management_patient p
                        WHERE p.id = a.patient_id
                    )
                """)
                orphaned_appointments = cursor.fetchone()[0]
                
                if orphaned_appointments > 0:
                    issues.append(f"❌ {orphaned_appointments} appointments with invalid patient references")
                else:
                    print("✅ All appointments have valid patient references")
                
                # Check for duplicate patient IDs
                cursor.execute("""
                    SELECT patient_id, COUNT(*) as count
                    FROM patient_management_patient
                    GROUP BY patient_id
                    HAVING COUNT(*) > 1
                """)
                duplicates = cursor.fetchall()
                
                if duplicates:
                    issues.append(f"❌ Duplicate patient IDs found: {duplicates}")
                else:
                    print("✅ No duplicate patient IDs")
                
        except Exception as e:
            issues.append(f"❌ Database analysis failed: {e}")
        
        self.analysis_results['database'] = {
            'status': 'healthy' if not issues else 'issues_found',
            'issues': issues
        }
        
        return len(issues) == 0
    
    def _analyze_frontend_issues(self):
        """Analyze frontend code issues"""
        print("\n🎨 Analyzing Frontend...")
        
        issues = []
        
        # Check for common frontend issues
        frontend_dir = Path('frontend/src')
        if frontend_dir.exists():
            # Check TypeScript files
            ts_files = list(frontend_dir.rglob('*.ts')) + list(frontend_dir.rglob('*.tsx'))
            
            for file_path in ts_files[:10]:  # Limit to first 10 files for quick check
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        # Check for any types
                        if ': any' in content:
                            issues.append(f"⚠️ {file_path.name} contains 'any' types")
                        
                        # Check for console.log
                        if 'console.log(' in content:
                            issues.append(f"⚠️ {file_path.name} contains console.log")
                            
                except Exception as e:
                    issues.append(f"❌ Error reading {file_path}: {e}")
        else:
            issues.append("❌ Frontend directory not found")
        
        self.analysis_results['frontend'] = {
            'status': 'clean' if not issues else 'issues_found',
            'issues': issues
        }
        
        return len(issues) == 0
    
    def generate_fix_recommendations(self):
        """Generate fix recommendations based on analysis"""
        print("\n💡 GENERATING FIX RECOMMENDATIONS")
        print("=" * 50)
        
        recommendations = []
        
        # Integration health recommendations
        if self.analysis_results.get('integration_health', {}).get('status') == 'issues_found':
            recommendations.append({
                'category': 'Integration Health',
                'priority': 'CRITICAL',
                'actions': [
                    'Fix token storage inconsistencies',
                    'Correct patient ID formats',
                    'Restore integration standards'
                ]
            })
        
        # Authentication recommendations
        if self.analysis_results.get('authentication', {}).get('status') == 'issues_found':
            recommendations.append({
                'category': 'Authentication',
                'priority': 'HIGH',
                'actions': [
                    'Check JWT token configuration',
                    'Verify login endpoint functionality',
                    'Test authentication middleware'
                ]
            })
        
        # API recommendations
        if self.analysis_results.get('api_endpoints', {}).get('status') == 'issues_found':
            recommendations.append({
                'category': 'API Endpoints',
                'priority': 'HIGH',
                'actions': [
                    'Fix endpoint response formats',
                    'Ensure pagination is working',
                    'Check URL routing configuration'
                ]
            })
        
        # Database recommendations
        if self.analysis_results.get('database', {}).get('status') == 'issues_found':
            recommendations.append({
                'category': 'Database',
                'priority': 'MEDIUM',
                'actions': [
                    'Clean up orphaned records',
                    'Fix foreign key constraints',
                    'Run data consistency checks'
                ]
            })
        
        self.fix_recommendations = recommendations
        
        for rec in recommendations:
            print(f"\n🔧 {rec['category']} ({rec['priority']} Priority)")
            for action in rec['actions']:
                print(f"   • {action}")
        
        if not recommendations:
            print("✅ No critical issues found - system appears healthy!")
        
        return recommendations
    
    def generate_automated_fixes(self):
        """Generate automated fixes for common issues"""
        print("\n🤖 GENERATING AUTOMATED FIXES")
        print("=" * 50)

        fixes_applied = []

        # Fix 1: Token storage consistency
        if any('token storage' in issue for issue in
               self.analysis_results.get('integration_health', {}).get('issues', [])):
            fixes_applied.extend(self._fix_token_storage())

        # Fix 2: Patient ID format
        if any('patient ID' in issue.lower() for issue in
               self.analysis_results.get('integration_health', {}).get('issues', [])):
            fixes_applied.extend(self._fix_patient_ids())

        # Fix 3: API response format
        if any('results' in issue for issue in
               self.analysis_results.get('api_endpoints', {}).get('issues', [])):
            fixes_applied.extend(self._fix_api_responses())

        return fixes_applied

    def _fix_token_storage(self):
        """Automatically fix token storage inconsistencies"""
        fixes = []

        frontend_files = [
            'frontend/src/utils/api.ts',
            'frontend/src/store/slices/authSlice.ts',
            'frontend/src/services/aiService.ts'
        ]

        for file_path in frontend_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Fix inconsistent token storage
                    if "localStorage.getItem('access_token')" in content:
                        new_content = content.replace(
                            "localStorage.getItem('access_token')",
                            "localStorage.getItem('token')"
                        )
                        new_content = new_content.replace(
                            "localStorage.setItem('access_token'",
                            "localStorage.setItem('token'"
                        )
                        new_content = new_content.replace(
                            "localStorage.removeItem('access_token')",
                            "localStorage.removeItem('token')"
                        )

                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(new_content)

                        fixes.append(f"Fixed token storage in {file_path}")

                except Exception as e:
                    fixes.append(f"Failed to fix {file_path}: {e}")

        return fixes

    def _fix_patient_ids(self):
        """Automatically fix patient ID formats"""
        fixes = []

        try:
            from patient_management.models import Patient

            patients = Patient.objects.all()
            fixed_count = 0

            for patient in patients:
                if not patient.patient_id.startswith('P') or len(patient.patient_id) != 7:
                    # Generate new proper ID
                    new_id = f"P{patient.id:06d}"

                    # Ensure uniqueness
                    while Patient.objects.filter(patient_id=new_id).exists():
                        new_id = f"P{patient.id + 1000:06d}"

                    patient.patient_id = new_id
                    patient.save()
                    fixed_count += 1

            if fixed_count > 0:
                fixes.append(f"Fixed {fixed_count} patient ID formats")

        except Exception as e:
            fixes.append(f"Failed to fix patient IDs: {e}")

        return fixes

    def _fix_api_responses(self):
        """Fix API response format issues"""
        fixes = []

        # This would involve checking and fixing serializers
        # For now, we'll provide guidance
        fixes.append("Manual fix required: Check serializers include 'id' field in create operations")

        return fixes

    def generate_bug_report(self):
        """Generate comprehensive bug report"""
        print("\n📋 BUG ANALYSIS REPORT")
        print("=" * 50)

        # Try automated fixes first
        automated_fixes = self.generate_automated_fixes()

        report = {
            'bug_info': self.bug_report,
            'analysis_results': self.analysis_results,
            'fix_recommendations': self.fix_recommendations,
            'automated_fixes': automated_fixes,
            'next_steps': []
        }

        # Determine next steps
        critical_issues = any(
            result.get('status') in ['issues_found', 'unhealthy']
            for result in self.analysis_results.values()
        )

        if critical_issues:
            report['next_steps'] = [
                '1. Review automated fixes applied',
                '2. Address remaining critical issues',
                '3. Run comprehensive integration tests',
                '4. Validate fix doesn\'t introduce regressions',
                '5. Update tests to prevent future occurrences'
            ]
        else:
            report['next_steps'] = [
                '1. System appears healthy',
                '2. Consider running full integration test suite',
                '3. Monitor for related issues'
            ]

        # Save report
        report_file = f"bug_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        print(f"📄 Report saved to: {report_file}")

        if automated_fixes:
            print(f"\n🤖 AUTOMATED FIXES APPLIED:")
            for fix in automated_fixes:
                print(f"   ✅ {fix}")

        return report


def main():
    """Main bug fixing function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='HMS Bug Fixer')
    parser.add_argument('--description', '-d', required=True, help='Bug description')
    parser.add_argument('--error', '-e', help='Error message')
    parser.add_argument('--component', '-c', help='Affected component')
    parser.add_argument('--steps', '-s', nargs='+', help='Steps to reproduce')
    
    args = parser.parse_args()
    
    fixer = HMSBugFixer()
    
    # Analyze the bug
    fixer.analyze_bug(
        bug_description=args.description,
        error_message=args.error,
        component=args.component,
        steps_to_reproduce=args.steps
    )
    
    # Generate recommendations
    fixer.generate_fix_recommendations()
    
    # Generate report
    fixer.generate_bug_report()


if __name__ == '__main__':
    main()
