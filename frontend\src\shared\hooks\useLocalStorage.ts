/** * Local Storage Hook * Provides consistent local storage operations with type safety and error handling */ import {
    useState, useEffect, useCallback
  } from 'react';
  export interface UseLocalStorageOptions
    <T> {
    serializer?: {
    serialize: (value: T) => string;
  deserialize: (value: string) => T;
  };
  onError?: (error: Error) => void;
  syncAcrossTabs?: boolean;
  } export interface UseLocalStorageResult
    <T> {
    value: T;
  setValue: (value: T | ((prev: T) => T)) => void;
  removeValue: () => void;
  loading: boolean;
  error: Error | null;
  }

const defaultSerializer = {
    serialize: JSON.stringify, deserialize: JSON.parse,
  }; /** * Hook for managing localStorage with type safety and error handling */ export

function useLocalStorage
    <T>( key: string, defaultValue: T, options: UseLocalStorageOptions
    <T> = {
  } ): UseLocalStorageResult
    <T> {
    const {
    serializer = defaultSerializer, onError, syncAcrossTabs = true,
  } = options;
  const [loading, setLoading] =
  useState(true);
  const [error, setError] = useState
    <Error | null>(null);
  const [storedValue, setStoredValue] = useState
    <T>(defaultValue); // Read from localStorage

const readValue =
  useCallback((): T => {
    if (typeof window === 'undefined') {
    return defaultValue;
  } try {
    const item = window.localStorage.getItem(key);
  if (item === null) {
    return defaultValue;
  } return serializer.deserialize(item);
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to read from localStorage');
  setError(err);
  onError?.(err);
  return defaultValue;
  }
  }, [key, defaultValue, serializer, onError]); // Write to localStorage

const writeValue =
  useCallback((value: T) => {
    if (typeof window === 'undefined') {
    return;
  } try {
    const serializedValue = serializer.serialize(value);
  window.localStorage.setItem(key, serializedValue);
  setStoredValue(value);
  setError(null);
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to write to localStorage');
  setError(err);
  onError?.(err);
  }
  }, [key, serializer, onError]); // Remove from localStorage

const removeValue =
  useCallback(() => {
    if (typeof window === 'undefined') {
    return;
  } try {
    window.localStorage.removeItem(key);
  setStoredValue(defaultValue);
  setError(null);
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to remove from localStorage');
  setError(err);
  onError?.(err);
  }
  }, [key, defaultValue, onError]); // Set value with

function support

const setValue =
  useCallback((value: T | ((prev: T) => T)) => {
    try {
    const newValue = value instanceof Function ? value(storedValue) : value;
  writeValue(newValue);
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to set localStorage value');
  setError(err);
  onError?.(err);
  }
  }, [storedValue, writeValue, onError]); // Initialize value from localStorage
  useEffect(() => {
    const initialValue = readValue();
  setStoredValue(initialValue);
  setLoading(false);
  }, [readValue]); // Listen for storage changes across tabs
  useEffect(() => {
    if (!syncAcrossTabs || typeof window === 'undefined') {
    return;
  }

const handleStorageChange = (e: StorageEvent) => {
    if (e.key === key && e.newValue !== null) {
    try {
    const newValue = serializer.deserialize(e.newValue);
  setStoredValue(newValue);
  setError(null);
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Failed to sync localStorage across tabs');
  setError(err);
  onError?.(err);
  }
  } else
  if (e.key === key && e.newValue === null) {
    setStoredValue(defaultValue);
  }
  };
  window.addEventListener('storage', handleStorageChange);
  return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, defaultValue, serializer, onError, syncAcrossTabs]);
  return {
    value: storedValue, setValue, removeValue, loading, error,
  };
  } /** * Hook for managing localStorage with automatic JSON serialization */ export

function useLocalStorageJSON
    <T>( key: string, defaultValue: T, options?: Omit
    <UseLocalStorageOptions<T>, 'serializer'> ): UseLocalStorageResult
    <T> {
    return
  useLocalStorage(key, defaultValue, { ...options, serializer: defaultSerializer,
  });
  } /** * Hook for managing localStorage with string values */ export

function
  useLocalStorageString( key: string, defaultValue: string = '', options?: Omit
    <UseLocalStorageOptions<string>, 'serializer'> ): UseLocalStorageResult<string> {
    return
  useLocalStorage(key, defaultValue, { ...options, serializer: {
    serialize: (value: string) => value, deserialize: (value: string) => value,
  },
  });
  } /** * Hook for managing localStorage with boolean values */ export

function
  useLocalStorageBoolean( key: string, defaultValue: boolean = false, options?: Omit
    <UseLocalStorageOptions<boolean>, 'serializer'> ): UseLocalStorageResult<boolean> {
    return
  useLocalStorage(key, defaultValue, { ...options, serializer: {
    serialize: (value: boolean) => value.toString(), deserialize: (value: string) => value === 'true',
  },
  });
  } /** * Hook for managing localStorage with number values */ export

function
  useLocalStorageNumber( key: string, defaultValue: number = 0, options?: Omit
    <UseLocalStorageOptions<number>, 'serializer'> ): UseLocalStorageResult<number> {
    return
  useLocalStorage(key, defaultValue, { ...options, serializer: {
    serialize: (value: number) => value.toString(), deserialize: (value: string) => {
    const parsed = parseFloat(value);
  if (isNaN(parsed)) {
    throw new Error(`Invalid number: ${
    value
  }`);
  } return parsed;
  },
  },
  });
  } /** * Hook for managing localStorage arrays */ export

function useLocalStorageArray
    <T>( key: string, defaultValue: T[] = [], options?: Omit
    <UseLocalStorageOptions<T[]>, 'serializer'> ): UseLocalStorageResult
    <T[]> & {
    addItem: (item: T) => void;
  removeItem: (index: number) => void;
  updateItem: (index: number, item: T) => void;
  clearArray: () => void;
  } {
    const result =
  useLocalStorageJSON(key, defaultValue, options);
  const addItem =
  useCallback((item: T) => {
    result.setValue(prev => [...prev, item]);
  }, [result]);
  const removeItem =
  useCallback((index: number) => {
    result.setValue(prev => prev.filter((_, i) => i !== index));
  }, [result]);
  const updateItem =
  useCallback((index: number, item: T) => {
    result.setValue(prev => prev.map((existingItem, i) => i === index ? item : existingItem));
  }, [result]);
  const clearArray =
  useCallback(() => {
    result.setValue([]);
  }, [result]);
  return { ...result, addItem, removeItem, updateItem, clearArray,
  };
  }
