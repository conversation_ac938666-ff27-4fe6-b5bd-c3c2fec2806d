/** * Shared Hooks Index * Centralized exports for all shared hooks */ // API and CRUD hooks export {
    useApi, useApiImmediate, useApiWithRetry
  } from './useApi';
  export {
    useCrud
  } from './useCrud'; // Form and validation hooks export {
    useForm
  } from './useForm';
  export type {
    FormField, FormState, ValidationRule, UseFormOptions, UseFormResult
  } from './useForm'; // Storage hooks export {
    useLocalStorage, useLocalStorageJSON, useLocalStorageString, useLocalStorageBoolean, useLocalStorageNumber, useLocalStorageArray
  } from './useLocalStorage';
  export type {
    UseLocalStorageOptions, UseLocalStorageResult
  } from './useLocalStorage'; // Performance hooks export {
    useDebounce, useDebounceCallback, useDebouncedState, useDebounceSearch, useDebounceValidation, useDebounceApi, useDebounceResize, useDebounceScroll
  } from './useDebounce'; // UI state hooks export {
    useModal, useModalStack, useConfirmModal, useFormModal
  } from './useModal';
  export type {
    ModalState, UseModalOptions, UseModalResult
  } from './useModal'; // Notification hooks export {
    useToast, useGlobalToast, useApiToast, useFormToast, useBatchToast
  } from './useToast';
  export type {
    Toast, ToastOptions, UseToastResult
  } from './useToast';
