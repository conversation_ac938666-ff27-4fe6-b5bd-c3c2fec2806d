import {
    BaseApiService
  } from '../shared/services/BaseApiService';
  import type {
    BaseEntity
  } from '../shared/types';
// AI Service Types export interface ChatMessage {
    role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: string;
  } export interface ChatRequest {
    conversation_id?: string;
  patient_id?: number;
  message: string;
  conversation_type?: 'diagnosis' | 'treatment' | 'consultation' | 'emergency' | 'general';
  } export interface ChatResponse {
    conversation_id: string;
  message_id: string;
  response: string;
  confidence_score?: number;
  medical_entities?: any;
  processing_time: number;
  success: boolean;
  } export interface DiagnosisRequest {
    patient_id: number;
  symptoms: string[];
  additional_info?: string;
  } export interface DiagnosisResponse {
    diagnosis_id: string;
  primary_diagnosis: string;
  differential_diagnoses: any[];
  confidence_score: number;
  reasoning: string;
  recommended_tests: string[];
  success: boolean;
  } export interface TreatmentRequest {
    patient_id: number;
  diagnosis_id?: string;
  diagnosis_text?: string;
  patient_preferences?: any;
  } export interface TreatmentResponse {
    recommendation_id: string;
  treatment_plan: string;
  medications: any[];
  procedures: any[];
  lifestyle_recommendations: string;
  confidence_score: number;
  evidence_level: string;
  success: boolean;
  } export interface RiskAssessmentRequest {
    patient_id: number;
  assessment_type?: 'comprehensive' | 'cardiovascular' | 'diabetes' | 'cancer' | 'surgical';
  } export interface RiskAssessmentResponse {
    assessment_id: string;
  overall_risk_score: number;
  risk_category: string;
  risk_factors: string[];
  preventive_measures: string;
  confidence_score: number;
  success: boolean;
  } export interface DrugInteractionRequest {
    patient_id: number;
  medications: Array<{
    name: string;
  dosage?: string;
  frequency?: string;
  }>;
  } export interface DrugInteractionResponse {
    check_id: string;
  interactions_found: any[];
  safety_score: number;
  risk_level: string;
  recommendations: string;
  alternative_medications: any[];
  success: boolean;
  } export interface MLPredictionRequest {
    model_name: string;
  input_data: any;
  patient_id?: number;
  } export interface MLPredictionResponse {
    prediction: any;
  confidence: number;
  model_used: string;
  success: boolean;
  error?: string;
  } export interface AIConversation extends BaseEntity {
    conversation_id: string;
  user: number;
  user_name: string;
  patient?: number;
  patient_name?: string;
  title: string;
  conversation_type: string;
  is_active: boolean;
  is_archived: boolean;
  messages: AIMessage[];
  } export interface AIMessage extends BaseEntity {
    message_id: string;
  role: string;
  content: string;
  model_used?: string;
  tokens_used: number;
  processing_time: number;
  medical_entities?: any;
  confidence_score?: number;
  } // AI Service Class class AIService extends BaseApiService
    <AIConversation> {
    constructor() {
    super({
    baseURL: '/ai', endpoints: {
    list: '/conversations/', detail: '/conversations/:id/', create: '/conversations/', update: '/conversations/:id/', delete: '/conversations/:id/',
  },
  });
  } // Chat with AI async sendMessage(request: ChatRequest): Promise
    <ChatResponse> {
    return this.post('/conversations/chat/', request);
  } // Get conversations async getConversations(): Promise
    <AIConversation[]> {
    const response = await this.get('/conversations/');
  return response.results || response;
  } // Get conversation by ID async getConversation(conversationId: string): Promise
    <AIConversation> {
    return this.get(`/conversations/${
    conversationId
  }/`);
  } // Generate diagnosis async generateDiagnosis(request: DiagnosisRequest): Promise
    <DiagnosisResponse> {
    return this.post('/diagnoses/generate_diagnosis/', request);
  } // Get diagnoses async getDiagnoses(): Promise<any[]> {
    const response = await this.get('/diagnoses/');
  return response.results || response;
  } // Generate treatment async generateTreatment(request: TreatmentRequest): Promise
    <TreatmentResponse> {
    return this.post('/treatments/generate_treatment/', request);
  } // Get treatments async getTreatments(): Promise<any[]> {
    const response = await this.get('/treatments/');
  return response.results || response;
  } // Assess risk async assessRisk(request: RiskAssessmentRequest): Promise
    <RiskAssessmentResponse> {
    return this.post('/risk-assessments/assess_risk/', request);
  } // Get risk assessments async getRiskAssessments(): Promise<any[]> {
    const response = await this.get('/risk-assessments/');
  return response.results || response;
  } // Check drug interactions async checkDrugInteractions(request: DrugInteractionRequest): Promise
    <DrugInteractionResponse> {
    return this.post('/drug-interactions/check_interactions/', request);
  } // Get drug interaction checks async getDrugInteractionChecks(): Promise<any[]> {
    const response = await this.get('/drug-interactions/');
  return response.results || response;
  } // ML prediction async makePrediction(request: MLPredictionRequest): Promise
    <MLPredictionResponse> {
    return this.post('/ml-models/predict/', request);
  } // Get ML models async getMLModels(): Promise<any[]> {
    const response = await this.get('/ml-models/');
  return response.results || response;
  } // Health check async getHealthStatus(): Promise<any> {
    return this.get('/health/status/');
  } // Archive conversation async archiveConversation(conversationId: string): Promise<void> {
    await this.patch(`/conversations/${
    conversationId
  }/`, {
    is_archived: true
  });
  } // Delete conversation async deleteConversation(conversationId: string): Promise<void> {
    await this.delete(`/conversations/${
    conversationId
  }/`);
  } // Update conversation title async updateConversationTitle(conversationId: string, title: string): Promise<void> {
    await this.patch(`/conversations/${
    conversationId
  }/`, {
    title
  });
  } // Get patient-specific AI data async getPatientAIData(patientId: number): Promise<{
    conversations: AIConversation[];
  diagnoses: any[];
  treatments: any[];
  riskAssessments: any[];
  drugChecks: any[];
  }> {
    const [conversations, diagnoses, treatments, riskAssessments, drugChecks] = await Promise.all([ this.get(`/conversations/?patient=${
    patientId
  }`), this.get(`/diagnoses/?patient=${
    patientId
  }`), this.get(`/treatments/?patient=${
    patientId
  }`), this.get(`/risk-assessments/?patient=${
    patientId
  }`), this.get(`/drug-interactions/?patient=${
    patientId
  }`) ]);
  return {
    conversations: conversations.results || conversations, diagnoses: diagnoses.results || diagnoses, treatments: treatments.results || treatments, riskAssessments: riskAssessments.results || riskAssessments, drugChecks: drugChecks.results || drugChecks,
  };
  }
  } export

const aiService = new AIService();
  export default aiService;
