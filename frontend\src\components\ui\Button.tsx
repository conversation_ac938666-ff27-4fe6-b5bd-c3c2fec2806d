import * as React from "react";
import {
    Slot
  } from "@radix-ui/react-slot";
import {
    cva, type VariantProps
  } from "class-variance-authority";
import {
    cn
  } from "../../lib/utils"

const buttonVariants = cva( "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50", {
    variants: {
    variant: {
    default: "bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg shadow-sm hover:shadow-md", destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 rounded-lg shadow-sm hover:shadow-md", outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-lg", secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 rounded-lg shadow-sm", ghost: "hover:bg-accent hover:text-accent-foreground rounded-lg", link: "text-primary underline-offset-4 hover:underline", glass: "btn-glass", "glass-primary": "btn-primary",
  }, size: {
    default: "h-10 px-4 py-2", sm: "h-9 px-3 text-xs", lg: "h-11 px-8 text-base", icon: "h-10 w-10",
  },
  }, defaultVariants: {
    variant: "default", size: "default",
  },
  } ) export interface ButtonProps extends React.ButtonHTMLAttributes
    <HTMLButtonElement>, VariantProps<typeof buttonVariants> {
    asChild?: boolean
  }

const Button = React.forwardRef
    <HTMLButtonElement, ButtonProps>( ({
    className, variant, size, asChild = false, ...props
  }, ref) => {
    const Comp = asChild ? Slot : "button"
  return (
    <Comp className={
    cn(buttonVariants({
    variant, size, className
  }))
  } ref={
    ref
  } {...props
  } /> )
  } ) Button.displayName = "Button" export {
    Button, buttonVariants
  }
