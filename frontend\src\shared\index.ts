/** * Shared Module Index * Main entry point for all shared functionality */ // Components export * from './components';
// Hooks export * from './hooks';
// Services export {
    BaseApiService
  } from './services/BaseApiService';
  export type {
    ApiServiceConfig, ApiEndpoints, RequestConfig
  } from './services/BaseApiService'; // Store utilities export {
    createCrudSlice
  } from './store/createCrudSlice';
  export type {
    CrudState, CrudOperation
  } from './store/createCrudSlice'; // Types export * from './types/api';
  export * from './types/common';
// Utilities export * from './utils';
