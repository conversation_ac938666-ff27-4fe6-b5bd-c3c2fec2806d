import React, {
    useState, useEffect
  } from 'react';
  import {
    useSelector
  } from 'react-redux';
  import type {
    RootState
  } from '../../store';
  import inventoryService, {
    InventoryItem
  } from '../../services/inventoryService';
  import {
    Button
  } from '../ui/Button';
  const InventoryDashboard: React.FC = () => {
    const {
    user
  } =
  useSelector((state: RootState) => state.auth);
  const [items, setItems] = useState
    <InventoryItem[]>([]);
  const [loading, setLoading] =
  useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedItem, setSelectedItem] = useState
    <InventoryItem | null>(null);
  const [filter, setFilter] =
  useState('all');
  const [showStockAdjustment, setShowStockAdjustment] = useState
    <InventoryItem | null>(null);
  const [stockAdjustment, setStockAdjustment] =
  useState({
    adjustment: 0, notes: ''
  });
  useEffect(() => {
    loadItems();
  }, [filter]);
  const loadItems = async () => {
    try {
    setLoading(true);
  let data;
  switch (filter) {
    case 'low_stock': data = await inventoryService.getLowStockItems();
  break;
  case 'out_of_stock': data = await inventoryService.getOutOfStockItems();
  break;
  case 'medicines': data = await inventoryService.getMedicines();
  break;
  case 'equipment': data = await inventoryService.getEquipment();
  break;
  default: data = await inventoryService.getInventoryItems();
  } setItems(data.results || data);
  setError(null);
  } catch (err) {
    setError('Failed to load inventory items');
  console.error('Error loading inventory items:', err);
  } finally {
    setLoading(false);
  }
  };
  const handleStockAdjustment = async () => {
    if (!showStockAdjustment) return;
  try {
    await inventoryService.adjustStock( showStockAdjustment.id, stockAdjustment.adjustment, stockAdjustment.notes );
  setShowStockAdjustment(null);
  setStockAdjustment({
    adjustment: 0, notes: ''
  });
  await loadItems();
  } catch (err) {
    setError('Failed to adjust stock');
  console.error('Error adjusting stock:', err);
  }
  };
  const getStockStatus = (item: InventoryItem) => {
    if (item.current_stock === 0) {
    return {
    status: 'Out of Stock', color: 'status-error'
  };
  } else
  if (item.is_low_stock) {
    return {
    status: 'Low Stock', color: 'status-warning'
  };
  } else {
    return {
    status: 'In Stock', color: 'status-success'
  };
  }
  };
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
    style: 'currency', currency: 'USD'
  }).format(amount);
  };
  if (loading) {
    return ( <div className="flex justify-center items-center h-64"> <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div> </div> );
  }
  return ( <div className="space-y-6"> {/* Header */
  } <div className="flex justify-between items-center"> <h1 className="text-2xl font-bold text-foreground">Inventory Management</h1> <div className="flex space-x-2">
    <Button className="bg-blue-600 hover:bg-blue-700 text-white"> Add Item
    </Button>
    <Button variant="outline"> Create Purchase Order
    </Button> </div> </div> {/* Quick Stats */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-4"> <div className="bg-background overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"> <span className="text-white text-sm font-medium">📦</span> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 truncate"> Total Items </dt> <dd className="text-lg font-medium text-foreground"> {
    items.length
  } </dd> </dl> </div> </div> </div> </div> <div className="bg-background overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"> <span className="text-white text-sm font-medium">⚠️</span> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 truncate"> Low Stock </dt> <dd className="text-lg font-medium text-foreground"> {
    items.filter(item => item.is_low_stock).length
  } </dd> </dl> </div> </div> </div> </div> <div className="bg-background overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center"> <span className="text-white text-sm font-medium">❌</span> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 truncate"> Out of Stock </dt> <dd className="text-lg font-medium text-foreground"> {
    items.filter(item => item.current_stock === 0).length
  } </dd> </dl> </div> </div> </div> </div> <div className="bg-background overflow-hidden shadow rounded-lg"> <div className="p-5"> <div className="flex items-center"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"> <span className="text-white text-sm font-medium">💰</span> </div> </div> <div className="ml-5 w-0 flex-1"> <dl> <dt className="text-sm font-medium text-gray-500 truncate"> Total Value </dt> <dd className="text-lg font-medium text-foreground"> {
    formatCurrency(items.reduce((sum, item) => sum + item.stock_value, 0))
  } </dd> </dl> </div> </div> </div> </div> </div> {/* Filters */
  } <div className="flex space-x-4">
    <Button onClick={() => setFilter('all')
  } variant={
    filter === 'all' ? 'default' : 'outline'
  } > All Items
    </Button>
    <Button onClick={() => setFilter('low_stock')
  } variant={
    filter === 'low_stock' ? 'default' : 'outline'
  } > Low Stock
    </Button>
    <Button onClick={() => setFilter('out_of_stock')
  } variant={
    filter === 'out_of_stock' ? 'default' : 'outline'
  } > Out of Stock
    </Button>
    <Button onClick={() => setFilter('medicines')
  } variant={
    filter === 'medicines' ? 'default' : 'outline'
  } > Medicines
    </Button>
    <Button onClick={() => setFilter('equipment')
  } variant={
    filter === 'equipment' ? 'default' : 'outline'
  } > Equipment
    </Button> </div> {/* Error Message */
  } {
    error && ( <div className="bg-red-100 border border-red-400 text-rose-700 dark:text-rose-400 px-4 py-3 rounded"> {
    error
  } </div> )
  } {/* Items Table */
  } <div className="bg-background shadow overflow-hidden sm:rounded-md"> <div className="px-4 py-5 sm:p-6"> <h3 className="text-lg leading-6 font-medium text-foreground mb-4"> Inventory Items </h3> <div className="overflow-x-auto"> <table className="min-w-full divide-y divide-gray-200"> <thead className="bg-muted"> <tr> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Item </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Category </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Stock </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Status </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Unit Cost </th> <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"> Actions </th> </tr> </thead> <tbody className="bg-background divide-y divide-gray-200"> {
    items.length === 0 ? ( <tr> <td colSpan={6
  } className="px-6 py-4 text-center text-gray-500"> No items found </td> </tr> ) : ( items.map((item) => {
    const stockStatus = getStockStatus(item);
  return ( <tr key={
    item.id
  } className="hover:bg-muted"> <td className="px-6 py-4 whitespace-nowrap"> <div className="text-sm font-medium text-foreground"> {
    item.name
  } </div> <div className="text-sm text-gray-500"> SKU: {
    item.sku
  } </div> {
    item.strength && ( <div className="text-sm text-gray-500"> {
    item.strength
  } </div> )
  } </td> <td className="px-6 py-4 whitespace-nowrap"> <div className="text-sm text-foreground"> {
    item.category.name
  } </div> <div className="text-sm text-gray-500"> {
    item.item_type
  } </div> </td> <td className="px-6 py-4 whitespace-nowrap"> <div className="text-sm font-medium text-foreground"> {
    item.current_stock
  } </div> <div className="text-sm text-gray-500"> Min: {
    item.minimum_stock
  } | Reorder: {
    item.reorder_level
  } </div> </td> <td className="px-6 py-4 whitespace-nowrap"> <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
    stockStatus.color
  }`
  }> {
    stockStatus.status
  } </span> </td> <td className="px-6 py-4 whitespace-nowrap"> <div className="text-sm font-medium text-foreground"> {
    formatCurrency(item.unit_cost)
  } </div> <div className="text-sm text-gray-500"> Value: {
    formatCurrency(item.stock_value)
  } </div> </td> <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
    <Button size="sm" variant="outline" onClick={() => setSelectedItem(item)
  } > View
    </Button>
    <Button size="sm" onClick={() => setShowStockAdjustment(item)
  } className="bg-blue-600 hover:bg-blue-700 text-white" > Adjust Stock
    </Button> </td> </tr> );
  }) )
  } </tbody> </table> </div> </div> </div> {/* Stock Adjustment Modal */
  } {
    showStockAdjustment && ( <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"> <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-background"> <div className="mt-3"> <h3 className="text-lg font-medium text-foreground mb-4"> Adjust Stock - {
    showStockAdjustment.name
  } </h3> <div className="space-y-4"> <div> <label className="block text-sm font-medium text-foreground"> Current Stock: {
    showStockAdjustment.current_stock
  } </label> </div> <div> <label className="block text-sm font-medium text-foreground"> Adjustment (+ to add, - to subtract) </label> <input type="number" value={
    stockAdjustment.adjustment
  } onChange={(e) => setStockAdjustment({ ...stockAdjustment, adjustment: parseInt(e.target.value) || 0
  })
  } className="mt-1 block w-full border-border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" /> </div> <div> <label className="block text-sm font-medium text-foreground"> Notes </label> <textarea value={
    stockAdjustment.notes
  } onChange={(e) => setStockAdjustment({ ...stockAdjustment, notes: e.target.value
  })
  } rows={3
  } className="mt-1 block w-full border-border rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" /> </div> <div className="text-sm text-muted-foreground"> New Stock: {
    showStockAdjustment.current_stock + stockAdjustment.adjustment
  } </div> </div> <div className="mt-6 flex justify-end space-x-3">
    <Button onClick={() => setShowStockAdjustment(null)
  } variant="outline" > Cancel
    </Button>
    <Button onClick={
    handleStockAdjustment
  } className="bg-blue-600 hover:bg-blue-700 text-white" > Adjust Stock
    </Button> </div> </div> </div> </div> )
  } </div> );
  };
  export default InventoryDashboard;
