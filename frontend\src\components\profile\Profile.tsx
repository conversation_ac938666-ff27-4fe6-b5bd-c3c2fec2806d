import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    useSelector
  } from 'react-redux';
  import type {
    RootState
  } from '../../store';
  import {
    Card, CardContent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/Button';
  import {
    Input
  } from '../ui/Input';
  import {
    User, Mail, Phone, MapPin, Calendar, Edit, Save, X, Camera, Shield, Clock, Award
  } from 'lucide-react';
  import {
    getAvatarClass, getCardClass, getTextClass, getButtonClass
  } from '../../utils/styleUtils';
  const Profile: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const {
    user
  } =
  useSelector((state: RootState) => state.auth);
  const [isEditing, setIsEditing] =
  useState(false);
  const [formData, setFormData] =
  useState({
    firstName: user?.first_name || '', lastName: user?.last_name || '', email: user?.email || '', phone: '+****************', address: '123 Medical Center Dr, Healthcare City, HC 12345', department: 'Cardiology', specialization: 'Interventional Cardiology', licenseNumber: 'MD-12345-2024', experience: '8 years'
  });
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value
  }));
  };
  const handleSave = () => { // Here you would typically save to backend setIsEditing(false);
  };
  const handleCancel = () => { // Reset form data setFormData({
    firstName: user?.first_name || '', lastName: user?.last_name || '', email: user?.email || '', phone: '+****************', address: '123 Medical Center Dr, Healthcare City, HC 12345', department: 'Cardiology', specialization: 'Interventional Cardiology', licenseNumber: 'MD-12345-2024', experience: '8 years'
  });
  setIsEditing(false);
  };
  return ( <div className="space-y-6"> {/* Page Header */
  } <div className="flex items-center justify-between mb-8"> <div> <h1 className="text-3xl font-bold text-foreground dark:text-white">Profile</h1> <p className="text-muted-foreground dark:text-gray-300 mt-2"> Manage your personal information and professional details </p> </div> <div className="flex gap-3"> {
    isEditing ? ( <>
    <Button onClick={
    handleCancel
  } variant="outline" className="flex items-center gap-2">
    <X className="w-4 h-4" /> Cancel
    </Button>
    <Button onClick={
    handleSave
  } className="flex items-center gap-2">
    <Save className="w-4 h-4" /> Save Changes
    </Button> </> ) : (
    <Button onClick={() => setIsEditing(true)
  } className="flex items-center gap-2">
    <Edit className="w-4 h-4" /> Edit Profile
    </Button> )
  } </div> </div> <div className="grid grid-cols-1 lg:grid-cols-3 gap-6"> {/* Profile Picture & Basic Info */
  }
    <Card className={
    getCardClass('glass')
  }>
    <CardContent className="p-6"> <div className="text-center"> <div className="relative inline-block"> <div className={`w-32 h-32 rounded-full flex items-center justify-center shadow-lg mx-auto ${
    getAvatarClass('xl', true)
  }`
  }> <span className="text-4xl font-medium"> {
    user?.first_name?.charAt(0)
  }{
    user?.last_name?.charAt(0)
  } </span> </div> {
    isEditing && ( <button className={`absolute bottom-2 right-2 w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
    getButtonClass('primary')
  }`
  }>
    <Camera className="w-4 h-4" /> </button> )
  } </div> <div className="mt-4"> <h2 className={`text-xl font-bold ${
    getTextClass('heading')
  }`
  }> {
    user?.first_name
  } {
    user?.last_name
  } </h2> <p className={`capitalize font-medium ${
    getTextClass('subheading')
  }`
  }> {
    user?.role
  } </p> <p className={`text-sm mt-1 ${
    getTextClass('caption')
  }`
  }> {
    user?.email
  } </p> </div> <div className="mt-6 space-y-3"> <div className={`flex items-center justify-center gap-2 text-sm ${
    getTextClass('caption')
  }`
  }>
    <Shield className="w-4 h-4 status-success" /> <span>Verified Account</span> </div> <div className={`flex items-center justify-center gap-2 text-sm ${
    getTextClass('caption')
  }`
  }>
    <Clock className="w-4 h-4 status-info" /> <span>Member since 2022</span> </div> <div className={`flex items-center justify-center gap-2 text-sm ${
    getTextClass('caption')
  }`
  }>
    <Award className="w-4 h-4 text-yellow-500" /> <span>Professional Level</span> </div> </div> </div>
    </CardContent>
    </Card> {/* Personal Information */
  }
    <Card className="glass border-0 shadow-lg lg:col-span-2">
    <CardHeader>
    <CardTitle className="flex items-center gap-3 text-foreground dark:text-white">
    <User className="w-5 h-5 text-blue-500" /> Personal Information
    </CardTitle>
    </CardHeader>
    <CardContent className="space-y-6"> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <label className="text-sm font-medium text-foreground dark:text-gray-300 mb-2 block"> First Name </label> {
    isEditing ? (
    <Input value={
    formData.firstName
  } onChange={(e) => handleInputChange('firstName', e.target.value)
  } className="w-full" /> ) : ( <p className="text-foreground dark:text-white py-2">{
    formData.firstName
  }</p> )
  } </div> <div> <label className="text-sm font-medium text-foreground dark:text-gray-300 mb-2 block"> Last Name </label> {
    isEditing ? (
    <Input value={
    formData.lastName
  } onChange={(e) => handleInputChange('lastName', e.target.value)
  } className="w-full" /> ) : ( <p className="text-foreground dark:text-white py-2">{
    formData.lastName
  }</p> )
  } </div> </div> <div> <label className="text-sm font-medium text-foreground dark:text-gray-300 mb-2 block"> Email Address </label> {
    isEditing ? (
    <Input type="email" value={
    formData.email
  } onChange={(e) => handleInputChange('email', e.target.value)
  } className="w-full" /> ) : ( <p className="text-foreground dark:text-white py-2 flex items-center gap-2">
    <Mail className="w-4 h-4 text-gray-500" /> {
    formData.email
  } </p> )
  } </div> <div> <label className="text-sm font-medium text-foreground dark:text-gray-300 mb-2 block"> Phone Number </label> {
    isEditing ? (
    <Input type="tel" value={
    formData.phone
  } onChange={(e) => handleInputChange('phone', e.target.value)
  } className="w-full" /> ) : ( <p className="text-foreground dark:text-white py-2 flex items-center gap-2">
    <Phone className="w-4 h-4 text-gray-500" /> {
    formData.phone
  } </p> )
  } </div> <div> <label className="text-sm font-medium text-foreground dark:text-gray-300 mb-2 block"> Address </label> {
    isEditing ? (
    <Input value={
    formData.address
  } onChange={(e) => handleInputChange('address', e.target.value)
  } className="w-full" /> ) : ( <p className="text-foreground dark:text-white py-2 flex items-center gap-2">
    <MapPin className="w-4 h-4 text-gray-500" /> {
    formData.address
  } </p> )
  } </div>
    </CardContent>
    </Card> {/* Professional Information */
  } {(user?.role === 'doctor' || user?.role === 'nurse') && (
    <Card className="glass border-0 shadow-lg lg:col-span-3">
    <CardHeader>
    <CardTitle className="flex items-center gap-3 text-foreground dark:text-white">
    <Award className="w-5 h-5 text-purple-500" /> Professional Information
    </CardTitle>
    </CardHeader>
    <CardContent className="space-y-6"> <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"> <div> <label className="text-sm font-medium text-foreground dark:text-gray-300 mb-2 block"> Department </label> {
    isEditing ? (
    <Input value={
    formData.department
  } onChange={(e) => handleInputChange('department', e.target.value)
  } className="w-full" /> ) : ( <p className="text-foreground dark:text-white py-2">{
    formData.department
  }</p> )
  } </div> <div> <label className="text-sm font-medium text-foreground dark:text-gray-300 mb-2 block"> Specialization </label> {
    isEditing ? (
    <Input value={
    formData.specialization
  } onChange={(e) => handleInputChange('specialization', e.target.value)
  } className="w-full" /> ) : ( <p className="text-foreground dark:text-white py-2">{
    formData.specialization
  }</p> )
  } </div> <div> <label className="text-sm font-medium text-foreground dark:text-gray-300 mb-2 block"> License Number </label> {
    isEditing ? (
    <Input value={
    formData.licenseNumber
  } onChange={(e) => handleInputChange('licenseNumber', e.target.value)
  } className="w-full" /> ) : ( <p className="text-foreground dark:text-white py-2">{
    formData.licenseNumber
  }</p> )
  } </div> <div> <label className="text-sm font-medium text-foreground dark:text-gray-300 mb-2 block"> Experience </label> {
    isEditing ? (
    <Input value={
    formData.experience
  } onChange={(e) => handleInputChange('experience', e.target.value)
  } className="w-full" /> ) : ( <p className="text-foreground dark:text-white py-2">{
    formData.experience
  }</p> )
  } </div> </div>
    </CardContent>
    </Card> )
  } </div> </div> );
  };
  export default Profile;
