/** * Base API Service Class * Provides unified API handling with error management, JWT authentication, and common CRUD operations */ import axios from 'axios';
  import type {
    AxiosInstance, AxiosRequestConfig, AxiosResponse
  } from 'axios';
  import type {
    ApiResponse, PaginatedResponse, ApiError, RequestConfig, PaginationParams, FilterParams, BulkActionParams, ApiEndpoints, ApiServiceConfig, BaseEntity
  } from '../types';
  import {
    API_CONFIG
  } from '../utils/constants';
  export class BaseApiService
    <T extends BaseEntity = any> {
    protected api: AxiosInstance;
  protected baseURL: string;
  protected endpoints: ApiEndpoints;
  protected defaultParams: Record<string, any>;
  constructor(config: ApiServiceConfig) { // Combine the main API base URL with the service-specific path this.baseURL = `${
    API_CONFIG.BASE_URL
  }${
    config.baseURL
  }`;
  this.endpoints = config.endpoints;
  this.defaultParams = config.defaultParams || {
  }; // Create axios instance with default configuration this.api = axios.create({
    baseURL: this.baseURL, timeout: config.timeout || API_CONFIG.TIMEOUT, headers: { 'Content-Type': 'application/json',
  },
  });
  this.setupInterceptors();
  } /** * Setup request and response interceptors */ private setupInterceptors(): void { // Request interceptor - Add JWT token this.api.interceptors.request.use( (config) => {
    const token = this.getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${
    token
  }`;
  } return config;
  }, (error) => {
    return Promise.reject(this.handleError(error));
  } ); // Response interceptor - Handle common errors this.api.interceptors.response.use( (response) => {
    return this.processResponse(response);
  }, (error) => {
    return Promise.reject(this.handleError(error));
  } );
  } /** * Get authentication token from localStorage */ private getAuthToken(): string | null {
    return localStorage.getItem('token');
  } /** * Process successful API response */ private processResponse
    <R>(response: AxiosResponse): ApiResponse
    <R> {
    return {
    data: response.data.results || response.data.data || response.data, success: true, message: response.data.message, status: response.status, ...(response.data.pagination && {
    pagination: response.data.pagination
  }),
  };
  } /** * Handle API errors consistently */ private handleError(error: any): ApiError {
    const apiError: ApiError = {
    message: 'An unexpected error occurred', status: 500,
  };
  if (error.response) { // Server responded with error status apiError.status = error.response.status;
  apiError.message = error.response.data?.message || error.response.data?.error || error.message;
  apiError.details = error.response.data; // Handle specific status codes switch (error.response.status) {
    case 401: this.handleUnauthorized();
  apiError.message = 'Authentication required. Please log in again.';
  break;
  case 403: apiError.message = 'You do not have permission to perform this action.';
  break;
  case 404: apiError.message = 'The requested resource was not found.';
  break;
  case 422: apiError.message = 'Validation failed. Please check your input.';
  break;
  case 500: apiError.message = 'Server error. Please try again later.';
  break;
  }
  } else
  if (error.request) { // Network error apiError.message = 'Network error. Please check your connection.';
  apiError.status = 0;
  } return apiError;
  } /** * Handle unauthorized access */ private handleUnauthorized(): void {
    localStorage.removeItem('token');
  localStorage.removeItem('refreshToken'); // Redirect to login page window.location.href = '/login';
  } /** * Build URL with parameters */ private buildUrl(endpoint: string, params?: Record<string, any>): string {
    let url = endpoint; // Replace path parameters
  if (params) {
    Object.keys(params).forEach(key => {
    if (url.includes(`:${
    key
  }`)) {
    url = url.replace(`:${
    key
  }`, params[key]);
  delete params[key];
  }
  });
  } return url;
  } /** * Generic GET request */ protected async get
    <R = T>( endpoint: string, config?: RequestConfig ): Promise
    <ApiResponse<R>> {
    const url = this.buildUrl(endpoint, config?.params);
  const response = await this.api.get(url, {
    params: { ...this.defaultParams, ...config?.params
  }, ...config,
  });
  return response;
  } /** * Generic POST request */ protected async post
    <R = T>( endpoint: string, data?: any, config?: RequestConfig ): Promise
    <ApiResponse<R>> {
    const url = this.buildUrl(endpoint, config?.params);
  const response = await this.api.post(url, data, config);
  return response;
  } /** * Generic PUT request */ protected async put
    <R = T>( endpoint: string, data?: any, config?: RequestConfig ): Promise
    <ApiResponse<R>> {
    const url = this.buildUrl(endpoint, config?.params);
  const response = await this.api.put(url, data, config);
  return response;
  } /** * Generic PATCH request */ protected async patch
    <R = T>( endpoint: string, data?: any, config?: RequestConfig ): Promise
    <ApiResponse<R>> {
    const url = this.buildUrl(endpoint, config?.params);
  const response = await this.api.patch(url, data, config);
  return response;
  } /** * Generic DELETE request */ protected async delete( endpoint: string, config?: RequestConfig ): Promise<void> {
    const url = this.buildUrl(endpoint, config?.params);
  await this.api.delete(url, config);
  } // CRUD Operations /** * Get all items with pagination and filtering */ async getAll(params?: PaginationParams & FilterParams): Promise
    <PaginatedResponse<T>> {
    return this.get
    <T[]>(this.endpoints.list, {
    params
  });
  } /** * Get single item by ID */ async getById(id: string | number): Promise
    <ApiResponse<T>> {
    return this.get
    <T>(this.endpoints.detail, {
    params: {
    id
  }
  });
  } /** * Create new item */ async create(data: Partial
    <T>): Promise
    <ApiResponse<T>> {
    const endpoint = this.endpoints.create || this.endpoints.list;
  return this.post
    <T>(endpoint, data);
  } /** * Update existing item */ async update(id: string | number, data: Partial
    <T>): Promise
    <ApiResponse<T>> {
    const endpoint = this.endpoints.update || this.endpoints.detail;
  return this.patch
    <T>(endpoint, data, {
    params: {
    id
  }
  });
  } /** * Delete item by ID */ async deleteById(id: string | number): Promise<void> {
    const endpoint = this.endpoints.delete || this.endpoints.detail;
  return this.delete(endpoint, {
    params: {
    id
  }
  });
  } /** * Bulk delete items */ async bulkDelete(ids: (string | number)[]): Promise<void> {
    const endpoint = `${
    this.endpoints.list
  }bulk_delete/`;
  await this.post(endpoint, {
    ids
  });
  } /** * Custom action on item */ async customAction
    <R = any>( id: string | number, action: string, data?: any ): Promise
    <ApiResponse<R>> {
    const endpoint = `${
    this.endpoints.detail
  }${
    action
  }/`;
  return this.post
    <R>(endpoint, data, {
    params: {
    id
  }
  });
  } /** * Search items */ async search(query: string, params?: FilterParams): Promise
    <PaginatedResponse<T>> {
    return this.get
    <T[]>(this.endpoints.list, {
    params: {
    search: query, ...params
  }
  });
  } /** * Get items with specific filters */ async filter(filters: FilterParams): Promise
    <PaginatedResponse<T>> {
    return this.get
    <T[]>(this.endpoints.list, {
    params: filters
  });
  } /** * Refresh authentication token */ async refreshToken(): Promise<void> {
    const refreshToken = localStorage.getItem('refreshToken');
  if (!refreshToken) {
    throw new Error('No refresh token available');
  } try {
    const response = await this.post('/auth/refresh/', {
    refresh: refreshToken
  });
  const {
    access, refresh
  } = response.data;
  localStorage.setItem('token', access);
  if (refresh) {
    localStorage.setItem('refreshToken', refresh);
  }
  } catch (error) {
    this.handleUnauthorized();
  throw error;
  }
  }
  }
