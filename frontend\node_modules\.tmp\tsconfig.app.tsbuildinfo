{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/admin/crudmanagement.tsx", "../../src/components/admin/reportsanalytics.tsx", "../../src/components/admin/usermanagement.tsx", "../../src/components/admin/userregistration.tsx", "../../src/components/ai/aichat.tsx", "../../src/components/ai/aiconfigmanager.tsx", "../../src/components/ai/aidashboard.tsx", "../../src/components/ai/aidiagnosis.tsx", "../../src/components/ai/emergencyactivation.tsx", "../../src/components/ai/floatingaichat.tsx", "../../src/components/ai/multiagentcaseprocessor.tsx", "../../src/components/ai/multiagentdashboard.tsx", "../../src/components/appointments/appointmentcalendar.tsx", "../../src/components/appointments/appointmentmanagement.refactored.tsx", "../../src/components/appointments/appointmentmanagement.tsx", "../../src/components/auth/loginform.tsx", "../../src/components/auth/protectedroute.tsx", "../../src/components/auth/registerform.tsx", "../../src/components/auth/restrictedaccess.tsx", "../../src/components/billing/billingdashboard.tsx", "../../src/components/billing/billingmanagement.tsx", "../../src/components/common/errorboundary.tsx", "../../src/components/communications/communicationsmanagement.tsx", "../../src/components/crud/cruddatatable.tsx", "../../src/components/dashboard/admindashboard.tsx", "../../src/components/dashboard/analyticsdashboard.tsx", "../../src/components/dashboard/dashboard.tsx", "../../src/components/dashboard/doctordashboard.tsx", "../../src/components/dashboard/nursedashboard.tsx", "../../src/components/dashboard/patientdashboard.tsx", "../../src/components/dashboard/receptionistdashboard.tsx", "../../src/components/dashboard/roledashboard.tsx", "../../src/components/demo/arabiclocalizationdemo.tsx", "../../src/components/demo/styledemo.tsx", "../../src/components/demo/styleguide.tsx", "../../src/components/doctors/medicalnotes.tsx", "../../src/components/doctors/mypatients.tsx", "../../src/components/doctors/myschedule.tsx", "../../src/components/emergency/emergencydashboard.tsx", "../../src/components/emergency/emergencymanagement.tsx", "../../src/components/forms/appointmentform.tsx", "../../src/components/inventory/inventorydashboard.tsx", "../../src/components/inventory/inventorymanagement.tsx", "../../src/components/landing/featuressection.tsx", "../../src/components/landing/footer.tsx", "../../src/components/landing/header.tsx", "../../src/components/landing/herosection.tsx", "../../src/components/landing/landingpage.tsx", "../../src/components/landing/testimonialssection.tsx", "../../src/components/medical/labtestmanagement.tsx", "../../src/components/medical/medicalrecords.tsx", "../../src/components/medical/prescriptionmanagement.tsx", "../../src/components/nurses/medicationadministration.tsx", "../../src/components/nurses/patientcare.tsx", "../../src/components/patient/appointmentbooking.tsx", "../../src/components/patients/myappointments.tsx", "../../src/components/patients/mylabresults.tsx", "../../src/components/patients/mymedicalhistory.tsx", "../../src/components/patients/myprescriptions.tsx", "../../src/components/patients/patientlist.tsx", "../../src/components/patients/patientregistrationform.tsx", "../../src/components/profile/profile.tsx", "../../src/components/settings/settings.tsx", "../../src/components/staff/staffmanagement.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/crudmodal.tsx", "../../src/components/ui/datatable.tsx", "../../src/components/ui/dropdown.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/languageswitcher.tsx", "../../src/components/ui/modal.tsx", "../../src/components/ui/notification.tsx", "../../src/components/ui/progress.tsx", "../../src/components/ui/switch.tsx", "../../src/components/ui/themetoggle.tsx", "../../src/components/ui/tooltip.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/checkbox.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/radio-group.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/textarea.tsx", "../../src/contexts/themecontext.tsx", "../../src/hooks/usetheme.ts", "../../src/i18n/index.ts", "../../src/lib/utils.ts", "../../src/pages/aiservices.tsx", "../../src/pages/styledemo.tsx", "../../src/services/aiservice.ts", "../../src/services/appointmentservice.ts", "../../src/services/billingservice.ts", "../../src/services/communicationsservice.ts", "../../src/services/crudservice.ts", "../../src/services/emergencyservice.ts", "../../src/services/inventoryservice.ts", "../../src/shared/index.ts", "../../src/shared/components/index.ts", "../../src/shared/components/data-display/datatable.tsx", "../../src/shared/components/data-display/metriccard.tsx", "../../src/shared/components/data-display/searchfilter.tsx", "../../src/shared/components/feedback/errorboundary.tsx", "../../src/shared/components/feedback/loadingspinner.tsx", "../../src/shared/components/feedback/modal.tsx", "../../src/shared/components/forms/formfield.tsx", "../../src/shared/components/layouts/dashboardlayout.tsx", "../../src/shared/data/mockpatientdata.ts", "../../src/shared/hooks/index.ts", "../../src/shared/hooks/useapi.ts", "../../src/shared/hooks/usecrud.ts", "../../src/shared/hooks/usedebounce.ts", "../../src/shared/hooks/useform.ts", "../../src/shared/hooks/uselocalstorage.ts", "../../src/shared/hooks/usemodal.ts", "../../src/shared/hooks/usetoast.ts", "../../src/shared/services/baseapiservice.ts", "../../src/shared/store/createcrudslice.ts", "../../src/shared/types/api.ts", "../../src/shared/types/common.ts", "../../src/shared/types/index.ts", "../../src/shared/utils/constants.ts", "../../src/shared/utils/dateutils.ts", "../../src/shared/utils/formatutils.ts", "../../src/shared/utils/index.ts", "../../src/store/index.ts", "../../src/store/slices/aislice.ts", "../../src/store/slices/appointmentslice.ts", "../../src/store/slices/authslice.ts", "../../src/store/slices/patientslice.ts", "../../src/store/slices/themeslice.ts", "../../src/store/slices/userslice.ts", "../../src/types/appointment.ts", "../../src/types/auth.ts", "../../src/types/billing.ts", "../../src/types/patient.ts", "../../src/utils/api.ts", "../../src/utils/healthcheck.ts", "../../src/utils/styleconverter.ts", "../../src/utils/styleutils.ts", "../../src/utils/validationutils.ts"], "errors": true, "version": "5.8.3"}