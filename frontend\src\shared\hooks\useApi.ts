/** * Generic API Hook * Provides consistent API call handling with loading states, error management, and caching */ import {
    useState, useEffect, useCallback, useRef
  } from 'react';
  import type {
    UseApiResult, ApiResponse
  } from '../types';
  interface UseApiOptions
    <T> {
    immediate?: boolean;
  dependencies?: any[];
  onSuccess?: (data: T) => void;
  onError?: (error: string) => void;
  retries?: number;
  retryDelay?: number;
  cache?: boolean;
  cacheKey?: string;
  cacheTTL?: number; // Time to live in milliseconds
  } // Simple in-memory cache

const apiCache = new Map<string, {
    data: any;
  timestamp: number;
  ttl: number
  }>(); /** * Generic hook for API calls with loading states and error handling */ export

function useApi
    <T = any>( apiCall: () => Promise
    <ApiResponse<T>>, options: UseApiOptions
    <T> = {
  } ): UseApiResult
    <T> {
    const {
    immediate = false, dependencies = [], onSuccess, onError, retries = 0, retryDelay = 1000, cache = false, cacheKey, cacheTTL = 5 * 60 * 1000, // 5 minutes default
  } = options;
  const [data, setData] = useState
    <T | null>(null);
  const [loading, setLoading] =
  useState(false);
  const [error, setError] = useState<string | null>(null);
  const mountedRef =
  useRef(true);
  const retryCountRef =
  useRef(0); // Generate cache key if not provided

const getCacheKey =
  useCallback(() => {
    if (cacheKey) return cacheKey;
  return `api_${
    apiCall.toString()
  }_${
    JSON.stringify(dependencies)
  }`;
  }, [cacheKey, apiCall, dependencies]); // Check cache for existing data

const getCachedData =
  useCallback(() => {
    if (!cache) return null;
  const key = getCacheKey();
  const cached = apiCache.get(key);
  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    return cached.data;
  } // Remove expired cache entry
  if (cached) {
    apiCache.delete(key);
  } return null;
  }, [cache, getCacheKey]); // Set cache data

const setCachedData =
  useCallback((newData: T) => {
    if (!cache) return;
  const key = getCacheKey();
  apiCache.set(key, {
    data: newData, timestamp: Date.now(), ttl: cacheTTL,
  });
  }, [cache, getCacheKey, cacheTTL]); // Execute API call with retry logic

const executeApiCall =
  useCallback(async (): Promise<void> => {
    if (!mountedRef.current) return; // Check cache first

const cachedData = getCachedData();
  if (cachedData) {
    setData(cachedData);
  setError(null);
  onSuccess?.(cachedData);
  return;
  } setLoading(true);
  setError(null);
  try {
    const response = await apiCall();
  if (!mountedRef.current) return;
  const responseData = response.data;
  setData(responseData); // Cache the data setCachedData(responseData); // Reset retry count on success retryCountRef.current = 0;
  onSuccess?.(responseData);
  } catch (err: any) {
    if (!mountedRef.current) return;
  const errorMessage = err.message || 'An error occurred'; // Retry logic
  if (retryCountRef.current < retries) {
    retryCountRef.current++;
  setTimeout(() => {
    if (mountedRef.current) {
    executeApiCall();
  }
  }, retryDelay * retryCountRef.current);
  return;
  } setError(errorMessage);
  onError?.(errorMessage);
  } finally {
    if (mountedRef.current) {
    setLoading(false);
  }
  }
  }, [ apiCall, retries, retryDelay, onSuccess, onError, getCachedData, setCachedData, ]); // Manual refetch

function

const refetch =
  useCallback(async (): Promise<void> => { // Clear cache for this key
  if (cache) {
    const key = getCacheKey();
  apiCache.delete(key);
  } retryCountRef.current = 0;
  await executeApiCall();
  }, [executeApiCall, cache, getCacheKey]); // Optimistic update

function

const mutate =
  useCallback((newData: T) => {
    setData(newData); // Update cache setCachedData(newData);
  }, [setCachedData]); // Effect to handle immediate execution and dependencies
  useEffect(() => {
    if (immediate) {
    executeApiCall();
  }
  }, [immediate, ...dependencies]); // Cleanup on unmount
  useEffect(() => {
    return () => {
    mountedRef.current = false;
  };
  }, []);
  return {
    data, loading, error, refetch, mutate,
  };
  } /** * Hook for API calls that should execute immediately */ export

function useApiImmediate
    <T = any>( apiCall: () => Promise
    <ApiResponse<T>>, dependencies: any[] = [], options?: Omit
    <UseApiOptions<T>, 'immediate' | 'dependencies'> ): UseApiResult
    <T> {
    return
  useApi(apiCall, { ...options, immediate: true, dependencies,
  });
  } /** * Hook for API calls with automatic retry */ export

function useApiWithRetry
    <T = any>( apiCall: () => Promise
    <ApiResponse<T>>, retries: number = 3, retryDelay: number = 1000, options?: Omit
    <UseApiOptions<T>, 'retries' | 'retryDelay'> ): UseApiResult
    <T> {
    return
  useApi(apiCall, { ...options, retries, retryDelay,
  });
  } /** * Hook for cached API calls */ export

function useApiCached
    <T = any>( apiCall: () => Promise
    <ApiResponse<T>>, cacheKey: string, cacheTTL: number = 5 * 60 * 1000, options?: Omit
    <UseApiOptions<T>, 'cache' | 'cacheKey' | 'cacheTTL'> ): UseApiResult
    <T> {
    return
  useApi(apiCall, { ...options, cache: true, cacheKey, cacheTTL,
  });
  } /** * Clear all API cache */ export

function clearApiCache(): void {
    apiCache.clear();
  } /** * Clear specific cache entry */ export

function clearCacheEntry(key: string): void {
    apiCache.delete(key);
  } /** * Get cache statistics */ export

function getCacheStats(): {
    size: number;
  keys: string[];
  totalMemory: number;
  } {
    const keys = Array.from(apiCache.keys());
  const totalMemory = keys.reduce((total, key) => {
    const entry = apiCache.get(key);
  return total + (entry ? JSON.stringify(entry.data).length : 0);
  }, 0);
  return {
    size: apiCache.size, keys, totalMemory,
  };
  }
