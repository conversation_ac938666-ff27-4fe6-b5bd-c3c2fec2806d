import {
    BaseApiService
  } from '../shared/services/BaseApiService';
  import type {
    BaseEntity
  } from '../shared/types/api';
  export interface EmergencyContact extends BaseEntity {
    name: string;
  organization: string;
  phone_primary: string;
  phone_secondary?: string;
  email?: string;
  contact_type: string;
  is_active: boolean;
  priority: number;
  } export interface EmergencyCase extends BaseEntity {
    case_id?: string;
  patient: number;
  attending_doctor: number;
  arrival_time: string;
  chief_complaint: string;
  triage_level: string;
  triage_nurse?: number;
  triage_time?: string;
  temperature?: number;
  blood_pressure_systolic?: number;
  blood_pressure_diastolic?: number;
  heart_rate?: number;
  respiratory_rate?: number;
  oxygen_saturation?: number;
  pain_scale?: number;
  gcs_eye?: number;
  gcs_verbal?: number;
  gcs_motor?: number;
  initial_assessment?: string;
  treatment_provided?: string;
  medications_given?: string;
  disposition?: string;
  discharge_time?: string;
  discharge_instructions?: string;
  status: string;
  } export interface TraumaCase extends BaseEntity {
    emergency_case: number;
  mechanism_of_injury: string;
  injury_description: string;
  iss_score?: number;
  rts_score?: number;
  head_neck: boolean;
  face: boolean;
  chest: boolean;
  abdomen: boolean;
  extremities: boolean;
  external: boolean;
  trauma_team_activated: boolean;
  activation_time?: string;
  intubated: boolean;
  chest_tube: boolean;
  blood_transfusion: boolean;
  surgery_required: boolean;
  } export interface CodeBlue extends BaseEntity {
    code_id?: string;
  patient: number;
  location: string;
  call_time: string;
  team_arrival_time?: string;
  event_end_time?: string;
  team_leader: number;
  team_members: number[];
  initial_rhythm: string;
  cpr_performed: boolean;
  defibrillation: boolean;
  number_of_shocks: number;
  intubation: boolean;
  iv_access: boolean;
  epinephrine_doses: number;
  atropine_doses: number;
  amiodarone_given: boolean;
  other_medications?: string;
  rosc_achieved: boolean;
  rosc_time?: string;
  outcome?: string;
  time_to_first_shock?: number;
  continuous_cpr: boolean;
  notes?: string;
  } export interface DisasterPlan extends BaseEntity {
    name: string;
  disaster_type: string;
  description: string;
  activation_criteria: string;
  response_procedures: string;
  required_staff: string;
  required_equipment: string;
  required_supplies: string;
  notification_list: string;
  communication_plan: string;
  is_active: boolean;
  last_reviewed: string;
  next_review_date: string;
  created_by?: number;
  } export interface DisasterActivation extends BaseEntity {
    activation_id?: string;
  plan: number;
  activated_by: number;
  activation_time: string;
  deactivation_time?: string;
  incident_description: string;
  estimated_casualties?: number;
  actual_casualties?: number;
  staff_recalled: number;
  additional_resources_requested?: string;
  status: string;
  lessons_learned?: string;
  improvements_needed?: string;
  } class EmergencyService extends BaseApiService
    <EmergencyCase> {
    constructor() {
    super({
    baseURL: '/emergency', endpoints: {
    list: '/cases/', detail: '/cases/:id/', create: '/cases/', update: '/cases/:id/', delete: '/cases/:id/',
  },
  });
  } // Emergency Contacts - using base service methods async getContacts(params?: any) {
    return this.get('/contacts/', {
    params
  });
  } async getContact(id: number) {
    return this.get('/contacts/:id/', {
    params: {
    id
  }
  });
  } async createContact(data: EmergencyContact) {
    return this.post('/contacts/', data);
  } async updateContact(id: number, data: Partial
    <EmergencyContact>) {
    return this.patch('/contacts/:id/', data, {
    params: {
    id
  }
  });
  } async deleteContact(id: number) {
    return this.delete('/contacts/:id/', {
    params: {
    id
  }
  });
  } async getActiveContacts() {
    return this.get('/contacts/active/');
  } async getContactsByType(type: string) {
    return this.get('/contacts/by_type/', {
    params: {
    type
  }
  });
  } // Emergency Cases - using inherited CRUD methods and custom actions async getActiveCases() {
    return this.get('/cases/active/');
  } async getCasesByTriage(level?: string) {
    return this.get('/cases/by_triage/', {
    params: {
    level
  }
  });
  } async getCaseStatistics() {
    return this.get('/cases/statistics/');
  } async triageCase(id: number, data: {
    triage_level: string;
  notes?: string
  }) {
    return this.customAction(id, 'triage', data);
  } async dischargeCase(id: number, data: {
    disposition: string;
  discharge_instructions?: string
  }) {
    return this.customAction(id, 'discharge', data);
  } // Trauma Cases - using base service methods async getTraumaCases(params?: any) {
    return this.get('/trauma/', {
    params
  });
  } async getTraumaCase(id: number) {
    return this.get('/trauma/:id/', {
    params: {
    id
  }
  });
  } async createTraumaCase(data: TraumaCase) {
    return this.post('/trauma/', data);
  } async updateTraumaCase(id: number, data: Partial
    <TraumaCase>) {
    return this.patch('/trauma/:id/', data, {
    params: {
    id
  }
  });
  } async deleteTraumaCase(id: number) {
    return this.delete('/trauma/:id/', {
    params: {
    id
  }
  });
  } async getHighSeverityTrauma() {
    return this.get('/trauma/high_severity/');
  } async activateTraumaTeam(id: number) {
    return this.post('/trauma/:id/activate_trauma_team/', {
  }, {
    params: {
    id
  }
  });
  } // Code Blue Events - using base service methods async getCodeBlueEvents(params?: any) {
    return this.get('/code-blue/', {
    params
  });
  } async getCodeBlueEvent(id: number) {
    return this.get('/code-blue/:id/', {
    params: {
    id
  }
  });
  } async createCodeBlueEvent(data: CodeBlue) {
    return this.post('/code-blue/', data);
  } async updateCodeBlueEvent(id: number, data: Partial
    <CodeBlue>) {
    return this.patch('/code-blue/:id/', data, {
    params: {
    id
  }
  });
  } async deleteCodeBlueEvent(id: number) {
    return this.delete('/code-blue/:id/', {
    params: {
    id
  }
  });
  } async getCodeBlueStatistics() {
    return this.get('/code-blue/statistics/');
  } // Disaster Plans - using base service methods async getDisasterPlans(params?: any) {
    return this.get('/disaster-plans/', {
    params
  });
  } async getDisasterPlan(id: number) {
    return this.get('/disaster-plans/:id/', {
    params: {
    id
  }
  });
  } async createDisasterPlan(data: DisasterPlan) {
    return this.post('/disaster-plans/', data);
  } async updateDisasterPlan(id: number, data: Partial
    <DisasterPlan>) {
    return this.patch('/disaster-plans/:id/', data, {
    params: {
    id
  }
  });
  } async deleteDisasterPlan(id: number) {
    return this.delete('/disaster-plans/:id/', {
    params: {
    id
  }
  });
  } async getActiveDisasterPlans() {
    return this.get('/disaster-plans/active/');
  } async getPlansForReview() {
    return this.get('/disaster-plans/due_for_review/');
  } // Disaster Activations - using base service methods async getDisasterActivations(params?: any) {
    return this.get('/disaster-activations/', {
    params
  });
  } async getDisasterActivation(id: number) {
    return this.get('/disaster-activations/:id/', {
    params: {
    id
  }
  });
  } async createDisasterActivation(data: DisasterActivation) {
    return this.post('/disaster-activations/', data);
  } async updateDisasterActivation(id: number, data: Partial
    <DisasterActivation>) {
    return this.patch('/disaster-activations/:id/', data, {
    params: {
    id
  }
  });
  } async deleteDisasterActivation(id: number) {
    return this.delete('/disaster-activations/:id/', {
    params: {
    id
  }
  });
  } async getActiveDisasterActivations() {
    return this.get('/disaster-activations/active/');
  } async deactivateDisaster(id: number, data: {
    lessons_learned?: string;
  improvements_needed?: string
  }) {
    return this.post('/disaster-activations/:id/deactivate/', data, {
    params: {
    id
  }
  });
  }
  } export

const emergencyService = new EmergencyService();
