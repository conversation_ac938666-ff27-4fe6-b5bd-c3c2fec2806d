import React, {
    useEffect, useState
  } from 'react';
  import {
    createPortal
  } from 'react-dom';
  import {
    X, CheckCircle, AlertCircle, Info, AlertTriangle
  } from 'lucide-react';
  import {
    cn
  } from '../../lib/utils';
  export type NotificationType = 'success' | 'error' | 'warning' | 'info';
  interface NotificationProps {
    id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  }

const Notification: React.FC
    <NotificationProps> = ({
    id, type, title, message, duration = 5000, onClose, position = 'top-right',
  }) => {
    const [isVisible, setIsVisible] =
  useState(false);
  const [isLeaving, setIsLeaving] =
  useState(false);
  useEffect(() => { // Trigger entrance animation

const timer = setTimeout(() => setIsVisible(true), 10);
  return () => clearTimeout(timer);
  }, []);
  useEffect(() => {
    if (duration > 0) {
    const timer = setTimeout(() => {
    handleClose();
  }, duration);
  return () => clearTimeout(timer);
  }
  }, [duration]);
  const handleClose = () => {
    setIsLeaving(true);
  setTimeout(() => {
    onClose(id);
  }, 300);
  };
  const icons = {
    success: CheckCircle, error: AlertCircle, warning: AlertTriangle, info: Info,
  };
  const colors = {
    success: 'text-green-500', error: 'text-red-500', warning: 'text-yellow-500', info: 'text-blue-500',
  };
  const Icon = icons[type];
  const positionClasses = { 'top-right': 'top-4 right-4', 'top-left': 'top-4 left-4', 'bottom-right': 'bottom-4 right-4', 'bottom-left': 'bottom-4 left-4',
  };
  return ( <div className={
    cn( 'fixed z-50 w-80 macos-notification', positionClasses[position], isVisible && !isLeaving && 'animate-in slide-in-from-right duration-300', isLeaving && 'animate-out slide-out-to-right duration-300' )
  } > <div className="flex items-start gap-3">
    <Icon className={
    cn('w-5 h-5 mt-0.5 flex-shrink-0', colors[type])
  } /> <div className="flex-1 min-w-0"> <h4 className="text-sm font-semibold macos-text-primary">{
    title
  }</h4> {
    message && ( <p className="text-sm macos-text-secondary mt-1">{
    message
  }</p> )
  } </div> <button onClick={
    handleClose
  } className="flex-shrink-0 p-1 macos-text-tertiary hover:macos-text-primary macos-transition" >
    <X className="w-4 h-4" /> </button> </div> </div> );
  }; // Notification Container interface NotificationContainerProps {
    notifications: Array<{
    id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  }>;
  onClose: (id: string) => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  }

const NotificationContainer: React.FC
    <NotificationContainerProps> = ({
    notifications, onClose, position = 'top-right',
  }) => {
    if (notifications.length === 0) return null;
  const content = ( <div className="fixed inset-0 pointer-events-none z-50"> <div className="relative h-full"> {
    notifications.map((notification, index) => ( <div key={
    notification.id
  } className="absolute pointer-events-auto" style={{ [position.includes('top') ? 'top' : 'bottom']: `${16 + index * 88
  }px`, [position.includes('right') ? 'right' : 'left']: '16px',
  }
  } >
    <Notification {...notification
  } onClose={
    onClose
  } position={
    position
  } /> </div> ))
  } </div> </div> );
  return createPortal(content, document.body);
  }; // Hook for managing notifications export

const useNotifications = () => {
    const [notifications, setNotifications] = useState
    <Array<{
    id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  }>>([]);
  const addNotification = ( type: NotificationType, title: string, message?: string, duration?: number ) => {
    const id = Math.random().toString(36).substr(2, 9);
  setNotifications(prev => [...prev, {
    id, type, title, message, duration
  }]);
  return id;
  };
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };
  const clearAll = () => {
    setNotifications([]);
  }; // Convenience methods

const success = (title: string, message?: string, duration?: number) => addNotification('success', title, message, duration);
  const error = (title: string, message?: string, duration?: number) => addNotification('error', title, message, duration);
  const warning = (title: string, message?: string, duration?: number) => addNotification('warning', title, message, duration);
  const info = (title: string, message?: string, duration?: number) => addNotification('info', title, message, duration);
  return {
    notifications, addNotification, removeNotification, clearAll, success, error, warning, info,
  };
  };
  export {
    Notification, NotificationContainer
  };
