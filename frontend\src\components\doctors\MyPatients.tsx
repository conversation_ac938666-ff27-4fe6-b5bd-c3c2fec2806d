import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    Card, CardContent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/Button';
  import {
    Badge
  } from '../ui/badge';
  import {
    Input
  } from '../ui/Input';
  import {
    useTheme
  } from '../../hooks/useTheme';
  import {
    Users, Search, Filter, Calendar, Phone, Mail, MapPin, AlertTriangle, Heart, Eye, FileText, Plus
  } from 'lucide-react';
  const MyPatients: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const {
    isDark
  } =
  useTheme();
  const [searchTerm, setSearchTerm] =
  useState('');
  const [filterStatus, setFilterStatus] =
  useState('all');
  const patients = [ {
    id: 1, name: '<PERSON>', age: 45, gender: 'Female', phone: '+****************', email: '<EMAIL>', address: '123 Main St, City, State', bloodType: 'A+', lastVisit: '2024-12-10', nextAppointment: '2024-12-20', status: 'stable', conditions: ['Hypertension', 'Type 2 Diabetes'], allergies: ['Penicillin'], emergencyContact: 'John Johnson - +****************', insurance: 'Blue Cross Blue Shield', notes: 'Regular follow-up for diabetes management'
  }, {
    id: 2, name: 'Michael Brown', age: 62, gender: 'Male', phone: '+****************', email: '<EMAIL>', address: '456 Oak Ave, City, State', bloodType: 'O-', lastVisit: '2024-12-08', nextAppointment: '2024-12-18', status: 'critical', conditions: ['Heart Disease', 'High Cholesterol'], allergies: ['Shellfish', 'Latex'], emergencyContact: 'Mary Brown - +****************', insurance: 'Medicare', notes: 'Post-surgery monitoring required'
  }, {
    id: 3, name: 'Emily Davis', age: 28, gender: 'Female', phone: '+****************', email: '<EMAIL>', address: '789 Pine St, City, State', bloodType: 'B+', lastVisit: '2024-12-05', nextAppointment: '2025-01-15', status: 'stable', conditions: ['Asthma'], allergies: [], emergencyContact: 'Robert Davis - +****************', insurance: 'Aetna', notes: 'Seasonal asthma management'
  }, {
    id: 4, name: 'Robert Wilson', age: 55, gender: 'Male', phone: '+****************', email: '<EMAIL>', address: '321 Elm St, City, State', bloodType: 'AB+', lastVisit: '2024-12-12', nextAppointment: '2024-12-16', status: 'monitoring', conditions: ['Chronic Pain', 'Arthritis'], allergies: ['Aspirin'], emergencyContact: 'Lisa Wilson - +****************', insurance: 'Cigna', notes: 'Pain management therapy ongoing'
  } ];
  const stats = {
    total: patients.length, stable: patients.filter(p => p.status === 'stable').length, critical: patients.filter(p => p.status === 'critical').length, monitoring: patients.filter(p => p.status === 'monitoring').length
  };
  const getStatusColor = (status: string) => {
    switch (status) {
    case 'stable': return 'status-success ';
  case 'critical': return 'status-error ';
  case 'monitoring': return 'status-warning ';
  default: return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300';
  }
  };
  const getStatusIcon = (status: string) => {
    switch (status) {
    case 'stable': return
    <Heart className="w-4 h-4 text-emerald-700 dark:text-emerald-400" />;
  case 'critical': return
    <AlertTriangle className="w-4 h-4 text-rose-700 dark:text-rose-400" />;
  case 'monitoring': return
    <Eye className="w-4 h-4 text-amber-700 dark:text-amber-400" />;
  default: return
    <Heart className="w-4 h-4 text-muted-foreground" />;
  }
  };
  const filteredPatients = patients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) || patient.email.toLowerCase().includes(searchTerm.toLowerCase()) || patient.conditions.some(condition => condition.toLowerCase().includes(searchTerm.toLowerCase()) );
  const matchesFilter = filterStatus === 'all' || patient.status === filterStatus;
  return matchesSearch && matchesFilter;
  });
  const renderPatientCard = (patient: any) => (
    <Card key={
    patient.id
  } className="glass border-0 shadow-lg hover:glass-hover macos-transition">
    <CardContent className="p-6"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center justify-between mb-4"> <div className="flex items-center space-x-3"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg"> <span className="text-white font-semibold text-lg"> {
    patient.name.split(' ').map((n: string) => n[0]).join('')
  } </span> </div> <div> <h3 className="text-lg font-semibold macos-text-primary">{
    patient.name
  }</h3> <p className="text-sm macos-text-secondary">{
    patient.age
  } years old • {
    patient.gender
  }</p> </div> </div>
    <Badge className={`${
    getStatusColor(patient.status)
  } rounded-full px-3 py-1 flex items-center gap-1`
  }> {
    getStatusIcon(patient.status)
  } <span>{
    patient.status
  }</span>
    </Badge> </div> <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4"> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Phone className="w-4 h-4 macos-text-tertiary" /> <span>{
    patient.phone
  }</span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Mail className="w-4 h-4 macos-text-tertiary" /> <span>{
    patient.email
  }</span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Heart className="w-4 h-4 macos-text-tertiary" /> <span>Blood Type: <span className="macos-text-primary font-medium">{
    patient.bloodType
  }</span></span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Calendar className="w-4 h-4 macos-text-tertiary" /> <span>Last Visit: <span className="macos-text-primary font-medium">{
    patient.lastVisit
  }</span></span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Calendar className="w-4 h-4 macos-text-tertiary" /> <span>Next: <span className="macos-text-primary font-medium">{
    patient.nextAppointment
  }</span></span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <MapPin className="w-4 h-4 macos-text-tertiary" /> <span>{
    patient.insurance
  }</span> </div> </div> <div className="space-y-3 mb-4"> <div> <span className="text-sm font-semibold macos-text-primary">Conditions: </span> <div className="inline-flex flex-wrap gap-2 mt-1"> {
    patient.conditions.map((condition: string, index: number) => (
    <Badge key={
    index
  } variant="outline" className="text-xs rounded-full px-2 py-1"> {
    condition
  }
    </Badge> ))
  } </div> </div> {
    patient.allergies.length > 0 && ( <div> <span className="text-sm font-semibold macos-text-primary">Allergies: </span> <div className="inline-flex flex-wrap gap-2 mt-1"> {
    patient.allergies.map((allergy: string, index: number) => (
    <Badge key={
    index
  } className="status-error text-xs rounded-full px-2 py-1"> {
    allergy
  }
    </Badge> ))
  } </div> </div> )
  } </div> {
    patient.notes && ( <div className="glass-subtle border /50 /50 rounded-xl p-4 mb-4"> <p className="text-sm text-sky-700 dark:text-sky-400 dark:text-blue-400"> <strong>Notes:</strong> {
    patient.notes
  } </p> </div> )
  } <div className="text-xs macos-text-secondary"> <strong>Emergency Contact:</strong> {
    patient.emergencyContact
  } </div> </div> <div className="flex flex-col space-y-2 ml-6">
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Eye className="w-4 h-4" /> View Chart
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <FileText className="w-4 h-4" /> Add Notes
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Calendar className="w-4 h-4" /> Schedule
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Phone className="w-4 h-4" /> Call
    </Button> </div> </div>
    </CardContent>
    </Card> );
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  }
    <Card className="glass border-0 shadow-xl">
    <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
    <Users className="w-6 h-6 text-white" /> </div> <div>
    <CardTitle className="text-2xl font-bold macos-text-primary">My Patients
    </CardTitle> <p className="macos-text-secondary">Manage your assigned patients</p> </div> </div>
    <Button variant="glass" className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Add New Patient
    </Button> </div>
    </CardHeader>
    </Card> {/* Statistics */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Total Patients</p> <p className="text-2xl font-bold macos-text-primary">{
    stats.total
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
    <Users className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Stable</p> <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400 dark:text-green-400">{
    stats.stable
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
    <Heart className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Critical</p> <p className="text-2xl font-bold text-rose-700 dark:text-rose-400 dark:text-red-400">{
    stats.critical
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
    <AlertTriangle className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Monitoring</p> <p className="text-2xl font-bold text-amber-700 dark:text-amber-400 dark:text-yellow-400">{
    stats.monitoring
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
    <Eye className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card> </div> {/* Search and Filter */
  }
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex flex-col sm:flex-row gap-4"> <div className="flex-1 relative">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 macos-text-tertiary w-4 h-4" />
    <Input type="text" placeholder="Search patients by name, email, or condition..." value={
    searchTerm
  } onChange={(e) => setSearchTerm(e.target.value)
  } className="pl-10" variant="glass" /> </div> <div className="flex items-center space-x-3">
    <Filter className="w-4 h-4 macos-text-secondary" /> <select value={
    filterStatus
  } onChange={(e) => setFilterStatus(e.target.value)
  } className="glass border-0 rounded-xl px-4 py-2 text-sm macos-text-primary focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" > <option value="all">All Patients</option> <option value="stable">Stable</option> <option value="critical">Critical</option> <option value="monitoring">Monitoring</option> </select> </div> </div>
    </CardContent>
    </Card> {/* Critical Patients Alert */
  } {
    stats.critical > 0 && (
    <Card className="glass border-0 shadow-lg /50 bg-red-50/50 dark:bg-red-900/20">
    <CardContent className="p-4"> <div className="flex items-center space-x-2">
    <AlertTriangle className="w-5 h-5 text-rose-700 dark:text-rose-400 dark:text-red-400" /> <p className="text-rose-700 dark:text-rose-400 dark:text-red-300"> <strong>Attention:</strong> You have {
    stats.critical
  } patient(s) in critical condition requiring immediate attention. </p> </div>
    </CardContent>
    </Card> )
  } {/* Patients List */
  } <div className="space-y-4"> {
    filteredPatients.length > 0 ? ( filteredPatients.map(renderPatientCard) ) : (
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
    <Users className="w-8 h-8 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary mb-2"> No patients found </h3> <p className="macos-text-secondary mb-4"> {
    searchTerm || filterStatus !== 'all' ? "No patients match your search criteria." : "You don't have any assigned patients yet."
  } </p> {(!searchTerm && filterStatus === 'all') && (
    <Button variant="glass" className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Add Your First Patient
    </Button> )
  }
    </CardContent>
    </Card> )
  } </div> </div> </div> );
  };
  export default MyPatients;
