import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    <PERSON>, CardContent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/Button';
  import {
    Badge
  } from '../ui/badge';
  import {
    useTheme
  } from '../../hooks/useTheme';
  import {
    Calendar, Clock, User, MapPin, Phone, Plus, Edit, X, CheckCircle, AlertCircle
  } from 'lucide-react';
  const MyAppointments: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const {
    isDark
  } =
  useTheme();
  const [activeTab, setActiveTab] =
  useState('upcoming');
  const appointments = {
    upcoming: [ {
    id: 1, date: '2024-12-15', time: '10:30 AM', doctor: 'Dr. <PERSON>', department: 'Cardiology', type: 'Follow-up', location: 'Room 205, 2nd Floor', status: 'confirmed', notes: 'Bring previous test results'
  }, {
    id: 2, date: '2024-12-22', time: '02:00 PM', doctor: 'Dr. <PERSON>', department: 'Endocrinology', type: 'Consultation', location: 'Room 310, 3rd Floor', status: 'pending', notes: 'Fasting required - no food 12 hours before'
  }, {
    id: 3, date: '2025-01-05', time: '09:00 AM', doctor: 'Dr. Emily Davis', department: 'General Medicine', type: 'Check-up', location: 'Room 101, 1st Floor', status: 'scheduled', notes: 'Annual health screening'
  } ], past: [ {
    id: 4, date: '2024-12-10', time: '11:00 AM', doctor: 'Dr. Sarah Johnson', department: 'Cardiology', type: 'Follow-up', location: 'Room 205, 2nd Floor', status: 'completed', notes: 'Blood pressure check completed'
  }, {
    id: 5, date: '2024-11-15', time: '03:30 PM', doctor: 'Dr. Michael Brown', department: 'Endocrinology', type: 'Consultation', location: 'Room 310, 3rd Floor', status: 'completed', notes: 'Diabetes management review'
  } ], cancelled: [ {
    id: 6, date: '2024-11-20', time: '10:00 AM', doctor: 'Dr. Robert Wilson', department: 'Orthopedics', type: 'Consultation', location: 'Room 405, 4th Floor', status: 'cancelled', notes: 'Cancelled due to doctor unavailability'
  } ]
  };
  const stats = {
    total: appointments.upcoming.length + appointments.past.length, upcoming: appointments.upcoming.length, completed: appointments.past.length, cancelled: appointments.cancelled.length
  };
  const tabs = [ {
    id: 'upcoming', label: 'Upcoming', count: stats.upcoming
  }, {
    id: 'past', label: 'Past', count: stats.completed
  }, {
    id: 'cancelled', label: 'Cancelled', count: stats.cancelled
  } ];
  const getStatusColor = (status: string) => {
    switch (status) {
    case 'confirmed': return 'status-success ';
  case 'pending': return 'status-warning ';
  case 'scheduled': return 'status-info ';
  case 'completed': return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300';
  case 'cancelled': return 'status-error ';
  default: return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300';
  }
  };
  const getStatusIcon = (status: string) => {
    switch (status) {
    case 'confirmed': return
    <CheckCircle className="w-4 h-4 text-emerald-700 dark:text-emerald-400" />;
  case 'pending': return
    <Clock className="w-4 h-4 text-amber-700 dark:text-amber-400" />;
  case 'scheduled': return
    <Calendar className="w-4 h-4 text-sky-700 dark:text-sky-400" />;
  case 'cancelled': return
    <X className="w-4 h-4 text-rose-700 dark:text-rose-400" />;
  default: return
    <AlertCircle className="w-4 h-4 text-muted-foreground" />;
  }
  };
  const renderAppointmentCard = (appointment: any) => (
    <Card key={
    appointment.id
  } className="glass border-0 shadow-lg hover:glass-hover macos-transition">
    <CardContent className="p-6"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center justify-between mb-4"> <div className="flex items-center space-x-3"> <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
    <User className="w-5 h-5 text-white" /> </div> <div> <h3 className="text-lg font-semibold macos-text-primary">{
    appointment.doctor
  }</h3>
    <Badge className={`${
    getStatusColor(appointment.status)
  } rounded-full px-3 py-1 flex items-center gap-1`
  }> {
    getStatusIcon(appointment.status)
  } <span>{
    appointment.status
  }</span>
    </Badge> </div> </div> <div className="text-sm macos-text-secondary font-medium"> {
    appointment.type
  } </div> </div> <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Calendar className="w-4 h-4 macos-text-tertiary" /> <span>{
    appointment.date
  }</span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Clock className="w-4 h-4 macos-text-tertiary" /> <span>{
    appointment.time
  }</span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <User className="w-4 h-4 macos-text-tertiary" /> <span>{
    appointment.department
  }</span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <MapPin className="w-4 h-4 macos-text-tertiary" /> <span>{
    appointment.location
  }</span> </div> </div> {
    appointment.notes && ( <div className="glass-subtle border /50 /50 rounded-xl p-4 mb-4"> <p className="text-sm text-sky-700 dark:text-sky-400 dark:text-blue-400"> <strong>Note:</strong> {
    appointment.notes
  } </p> </div> )
  } </div> <div className="flex flex-col space-y-2 ml-6"> {
    activeTab === 'upcoming' && ( <>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Edit className="w-4 h-4" /> Reschedule
    </Button>
    <Button variant="destructive" size="sm" className="flex items-center gap-2">
    <X className="w-4 h-4" /> Cancel
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Phone className="w-4 h-4" /> Call
    </Button> </> )
  } {
    activeTab === 'past' && ( <>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Book Again
    </Button>
    <Button variant="glass" size="sm"> View Report
    </Button> </> )
  } {
    activeTab === 'cancelled' && (
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Rebook
    </Button> )
  } </div> </div>
    </CardContent>
    </Card> );
  const getCurrentAppointments = () => {
    switch (activeTab) {
    case 'upcoming': return appointments.upcoming;
  case 'past': return appointments.past;
  case 'cancelled': return appointments.cancelled;
  default: return appointments.upcoming;
  }
  };
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  }
    <Card className="glass border-0 shadow-xl">
    <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
    <Calendar className="w-6 h-6 text-white" /> </div> <div>
    <CardTitle className="text-2xl font-bold macos-text-primary">My Appointments
    </CardTitle> <p className="macos-text-secondary">Manage your medical appointments</p> </div> </div>
    <Button variant="glass" className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Book New Appointment
    </Button> </div>
    </CardHeader>
    </Card> {/* Statistics */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Total Appointments</p> <p className="text-2xl font-bold macos-text-primary">{
    stats.total
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
    <Calendar className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Upcoming</p> <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400 dark:text-green-400">{
    stats.upcoming
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
    <Clock className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Completed</p> <p className="text-2xl font-bold macos-text-primary">{
    stats.completed
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center shadow-lg">
    <CheckCircle className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Cancelled</p> <p className="text-2xl font-bold text-rose-700 dark:text-rose-400 dark:text-red-400">{
    stats.cancelled
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
    <X className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card> </div> {/* Tabs */
  }
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <nav className="flex space-x-2"> {
    tabs.map((tab) => ( <button key={
    tab.id
  } onClick={() => setActiveTab(tab.id)
  } className={`flex items-center px-4 py-2 rounded-xl font-medium text-sm macos-transition ${
    activeTab === tab.id ? 'bg-blue-600 text-white shadow-lg' : 'macos-text-secondary hover:macos-text-primary hover:glass-hover'
  }`
  } > {
    tab.label
  } <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
    activeTab === tab.id ? 'bg-background/20 text-white' : 'bg-muted text-muted-foreground dark:bg-gray-800 dark:text-gray-300'
  }`
  }> {
    tab.count
  } </span> </button> ))
  } </nav>
    </CardContent>
    </Card> {/* Appointments List */
  } <div className="space-y-4"> {
    getCurrentAppointments().length > 0 ? ( getCurrentAppointments().map(renderAppointmentCard) ) : (
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
    <Calendar className="w-8 h-8 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary mb-2"> No {
    activeTab
  } appointments </h3> <p className="macos-text-secondary mb-4"> {
    activeTab === 'upcoming' ? "You don't have any upcoming appointments scheduled." : `No ${
    activeTab
  } appointments found.`
  } </p> {
    activeTab === 'upcoming' && (
    <Button variant="glass" className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Book Your First Appointment
    </Button> )
  }
    </CardContent>
    </Card> )
  } </div> </div> </div> );
  };
  export default MyAppointments;
