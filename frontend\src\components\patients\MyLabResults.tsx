import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/Button';
  import {
    Badge
  } from '../ui/badge';
  import {
    TestTube, Calendar, User, TrendingUp, TrendingDown, Minus, Download, Eye, AlertTriangle, CheckCircle, Clock
  } from 'lucide-react';
  import {
    getStatusClass, getSeverityClass
  } from '../../utils/styleUtils';
  const MyLabResults: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const [activeTab, setActiveTab] =
  useState('recent');
  const labResults = {
    recent: [ {
    id: 1, testName: 'Complete Blood Count (CBC)', orderDate: '2024-12-08', resultDate: '2024-12-10', orderedBy: 'Dr. <PERSON>', status: 'completed', category: 'Hematology', results: [ {
    parameter: 'Hemoglobin', value: '14.2', unit: 'g/dL', range: '12.0-15.5', status: 'normal'
  }, {
    parameter: 'White Blood Cells', value: '7.8', unit: '×10³/μL', range: '4.0-11.0', status: 'normal'
  }, {
    parameter: 'Platelets', value: '285', unit: '×10³/μL', range: '150-450', status: 'normal'
  } ], summary: 'All values within normal range'
  }, {
    id: 2, testName: 'HbA1c (Diabetes Monitoring)', orderDate: '2024-11-10', resultDate: '2024-11-12', orderedBy: 'Dr. Michael Brown', status: 'completed', category: 'Chemistry', results: [ {
    parameter: 'HbA1c', value: '7.2', unit: '%', range: '<7.0', status: 'high'
  } ], summary: 'Slightly elevated - diabetes management needed'
  }, {
    id: 3, testName: 'Lipid Panel', orderDate: '2024-10-15', resultDate: '2024-10-17', orderedBy: 'Dr. Emily Davis', status: 'completed', category: 'Chemistry', results: [ {
    parameter: 'Total Cholesterol', value: '195', unit: 'mg/dL', range: '<200', status: 'normal'
  }, {
    parameter: 'LDL Cholesterol', value: '125', unit: 'mg/dL', range: '<100', status: 'high'
  }, {
    parameter: 'HDL Cholesterol', value: '45', unit: 'mg/dL', range: '>40', status: 'normal'
  }, {
    parameter: 'Triglycerides', value: '150', unit: 'mg/dL', range: '<150', status: 'normal'
  } ], summary: 'LDL cholesterol elevated - dietary changes recommended'
  } ], pending: [ {
    id: 4, testName: 'Thyroid Function Test', orderDate: '2024-12-12', resultDate: null, orderedBy: 'Dr. Sarah Johnson', status: 'pending', category: 'Endocrinology', estimatedCompletion: '2024-12-15'
  }, {
    id: 5, testName: 'Vitamin D Level', orderDate: '2024-12-11', resultDate: null, orderedBy: 'Dr. Emily Davis', status: 'in_progress', category: 'Chemistry', estimatedCompletion: '2024-12-14'
  } ]
  };
  const stats = {
    completed: labResults.recent.length, pending: labResults.pending.length, abnormal: labResults.recent.filter(test => test.results?.some(result => result.status === 'high' || result.status === 'low') ).length, normal: labResults.recent.filter(test => test.results?.every(result => result.status === 'normal') ).length
  };
  const tabs = [ {
    id: 'recent', label: 'Recent Results', count: stats.completed
  }, {
    id: 'pending', label: 'Pending Tests', count: stats.pending
  } ];
  const getResultStatusColor = (status: string) => {
    switch (status) {
    case 'normal': return 'status-success';
  case 'high': return 'status-error';
  case 'low': return 'status-warning';
  default: return 'text-muted-foreground';
  }
  };
  const getResultStatusIcon = (status: string) => {
    switch (status) {
    case 'normal': return
    <CheckCircle className="w-4 h-4 text-emerald-700 dark:text-emerald-400" />;
  case 'high': return
    <TrendingUp className="w-4 h-4 text-rose-700 dark:text-rose-400" />;
  case 'low': return
    <TrendingDown className="w-4 h-4 text-orange-600" />;
  default: return
    <Minus className="w-4 h-4 text-muted-foreground" />;
  }
  };
  const renderCompletedTest = (test: any) => (
    <Card key={
    test.id
  } className="mb-4">
    <CardContent className="p-6"> <div className="flex items-start justify-between mb-4"> <div className="flex-1"> <div className="flex items-center justify-between mb-2"> <h3 className="text-lg font-semibold text-foreground">{
    test.testName
  }</h3>
    <Badge className={
    getStatusClass(test.status)
  }>
    <CheckCircle className="w-4 h-4 mr-1" /> {
    test.status
  }
    </Badge> </div> <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3 text-sm text-muted-foreground"> <div className="flex items-center space-x-2">
    <Calendar className="w-4 h-4" /> <span>Ordered: {
    test.orderDate
  }</span> </div> <div className="flex items-center space-x-2">
    <TestTube className="w-4 h-4" /> <span>Result: {
    test.resultDate
  }</span> </div> <div className="flex items-center space-x-2">
    <User className="w-4 h-4" /> <span>{
    test.orderedBy
  }</span> </div> </div> <div className="bg-muted rounded-lg p-4 mb-3"> <h4 className="font-medium text-foreground mb-3">Test Results</h4> <div className="space-y-2"> {
    test.results.map((result: any, index: number) => ( <div key={
    index
  } className="flex items-center justify-between py-2 border-b border-border last:border-b-0"> <div className="flex items-center space-x-2"> {
    getResultStatusIcon(result.status)
  } <span className="font-medium">{
    result.parameter
  }</span> </div> <div className="text-right"> <div className={`font-semibold ${
    getResultStatusColor(result.status)
  }`
  }> {
    result.value
  } {
    result.unit
  } </div> <div className="text-xs text-gray-500"> Range: {
    result.range
  } </div> </div> </div> ))
  } </div> </div> <div className="bg-blue-50 border rounded-lg p-3"> <p className="text-sm text-sky-700 dark:text-sky-400"> <strong>Summary:</strong> {
    test.summary
  } </p> </div> </div> <div className="flex flex-col space-y-2 ml-4">
    <Button variant="outline" size="sm">
    <Eye className="w-4 h-4 mr-1" /> View Details
    </Button>
    <Button variant="outline" size="sm">
    <Download className="w-4 h-4 mr-1" /> Download
    </Button> </div> </div>
    </CardContent>
    </Card> );
  const renderPendingTest = (test: any) => (
    <Card key={
    test.id
  } className="mb-4">
    <CardContent className="p-6"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center justify-between mb-2"> <h3 className="text-lg font-semibold text-foreground">{
    test.testName
  }</h3>
    <Badge className={
    getStatusClass(test.status)
  }>
    <Clock className="w-4 h-4 mr-1" /> {
    test.status.replace('_', ' ')
  }
    </Badge> </div> <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3 text-sm text-muted-foreground"> <div className="flex items-center space-x-2">
    <Calendar className="w-4 h-4" /> <span>Ordered: {
    test.orderDate
  }</span> </div> <div className="flex items-center space-x-2">
    <User className="w-4 h-4" /> <span>{
    test.orderedBy
  }</span> </div> <div className="flex items-center space-x-2">
    <TestTube className="w-4 h-4" /> <span>Category: {
    test.category
  }</span> </div> </div> {
    test.estimatedCompletion && ( <div className="bg-yellow-50 border rounded-lg p-3"> <p className="text-sm text-amber-700 dark:text-amber-400">
    <Clock className="w-4 h-4 inline mr-1" /> <strong>Estimated completion:</strong> {
    test.estimatedCompletion
  } </p> </div> )
  } </div> <div className="flex flex-col space-y-2 ml-4">
    <Button variant="outline" size="sm" disabled>
    <Clock className="w-4 h-4 mr-1" /> Pending
    </Button> </div> </div>
    </CardContent>
    </Card> );
  const getCurrentTests = () => {
    switch (activeTab) {
    case 'recent': return labResults.recent;
  case 'pending': return labResults.pending;
  default: return labResults.recent;
  }
  };
  return ( <div className="space-y-6 p-6"> {/* Header */
  } <div className="flex items-center justify-between"> <div> <h1 className="text-3xl font-bold text-foreground">My Lab Results</h1> <p className="text-lg text-muted-foreground mt-2">View and manage your laboratory test results</p> </div>
    <Button>
    <Download className="w-4 h-4 mr-2" /> Export All Results
    </Button> </div> {/* Statistics */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <Card>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Completed Tests</p> <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400">{
    stats.completed
  }</p> </div>
    <CheckCircle className="w-8 h-8 text-emerald-700 dark:text-emerald-400" /> </div>
    </CardContent>
    </Card>
    <Card>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Pending Tests</p> <p className="text-2xl font-bold text-amber-700 dark:text-amber-400">{
    stats.pending
  }</p> </div>
    <Clock className="w-8 h-8 text-amber-700 dark:text-amber-400" /> </div>
    </CardContent>
    </Card>
    <Card>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Normal Results</p> <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400">{
    stats.normal
  }</p> </div>
    <CheckCircle className="w-8 h-8 text-emerald-700 dark:text-emerald-400" /> </div>
    </CardContent>
    </Card>
    <Card>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Abnormal Results</p> <p className="text-2xl font-bold text-rose-700 dark:text-rose-400">{
    stats.abnormal
  }</p> </div>
    <AlertTriangle className="w-8 h-8 text-rose-700 dark:text-rose-400" /> </div>
    </CardContent>
    </Card> </div> {/* Abnormal Results Alert */
  } {
    stats.abnormal > 0 && (
    <Card className="bg-red-50">
    <CardContent className="p-4"> <div className="flex items-center space-x-2">
    <AlertTriangle className="w-5 h-5 text-rose-700 dark:text-rose-400" /> <p className="text-rose-700 dark:text-rose-400"> <strong>Attention:</strong> You have {
    stats.abnormal
  } test result(s) with abnormal values. Please consult with your doctor. </p> </div>
    </CardContent>
    </Card> )
  } {/* Tabs */
  } <div className="border-b border-border"> <nav className="-mb-px flex space-x-8"> {
    tabs.map((tab) => ( <button key={
    tab.id
  } onClick={() => setActiveTab(tab.id)
  } className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
    activeTab === tab.id ? 'border-blue-500 text-sky-700 dark:text-sky-400' : 'border-transparent text-gray-500 hover:text-foreground hover:border-border'
  }`
  } > {
    tab.label
  } <span className="ml-2 bg-muted text-muted-foreground py-0.5 px-2 rounded-full text-xs"> {
    tab.count
  } </span> </button> ))
  } </nav> </div> {/* Results List */
  } <div className="space-y-4"> {
    getCurrentTests().length > 0 ? ( getCurrentTests().map(test => activeTab === 'recent' ? renderCompletedTest(test) : renderPendingTest(test) ) ) : (
    <Card>
    <CardContent className="p-12 text-center">
    <TestTube className="w-12 h-12 text-gray-400 mx-auto mb-4" /> <h3 className="text-lg font-medium text-foreground mb-2"> No {
    activeTab
  } lab results </h3> <p className="text-muted-foreground"> {
    activeTab === 'recent' ? "You don't have any completed lab results yet." : "No pending lab tests found."
  } </p>
    </CardContent>
    </Card> )
  } </div> </div> );
  };
  export default MyLabResults;
