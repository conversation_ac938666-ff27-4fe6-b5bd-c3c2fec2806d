@tailwind base;
@tailwind components;
@tailwind utilities;

/* =================================
   HMS UNIFIED STYLE SYSTEM
   ================================= */

/* CSS Variables for Theme System */
@layer base {
  :root {
    /* Light Mode Colors */
    --background: 255 255 255;
    --foreground: 28 28 30;
    --card: 255 255 255;
    --card-foreground: 28 28 30;
    --popover: 255 255 255;
    --popover-foreground: 28 28 30;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 242 243 245;
    --secondary-foreground: 28 28 30;
    --muted: 242 243 245;
    --muted-foreground: 99 99 102;
    --accent: 248 249 250;
    --accent-foreground: 28 28 30;
    --destructive: 255 59 48;
    --destructive-foreground: 255 255 255;
    --border: 229 229 231;
    --input: 242 243 245;
    --ring: 59 130 246;
    --radius: 0.75rem;

    /* Glass Effects */
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-elevated: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .dark {
    /* Dark Mode Colors */
    --background: 0 0 0;
    --foreground: 255 255 255;
    --card: 28 28 30;
    --card-foreground: 255 255 255;
    --popover: 28 28 30;
    --popover-foreground: 255 255 255;
    --primary: 59 130 246;
    --primary-foreground: 255 255 255;
    --secondary: 44 44 46;
    --secondary-foreground: 255 255 255;
    --muted: 44 44 46;
    --muted-foreground: 152 152 157;
    --accent: 44 44 46;
    --accent-foreground: 255 255 255;
    --destructive: 255 59 48;
    --destructive-foreground: 255 255 255;
    --border: 56 56 58;
    --input: 44 44 46;
    --ring: 147 197 253;

    /* Glass Effects */
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-elevated: 0 12px 40px rgba(0, 0, 0, 0.4);
  }
}

/* Glassmorphism Effects - Consolidated */
@layer components {
  .glass {
    @apply backdrop-blur-md bg-card/80 border border-border/20 shadow-glass;
  }

  .glass-light {
    @apply backdrop-blur-sm bg-card/60 border border-border/10 shadow-sm;
  }

  .glass-heavy {
    @apply backdrop-blur-lg bg-card/90 border border-border/30 shadow-elevated;
  }
}

/* Sidebar styles */
@layer components {
  #main-dashboard-sidebar {
    @apply relative z-10;
  }

  .sidebar {
    @apply w-72 flex-shrink-0 flex flex-col;
  }

  .sidebar-header {
    @apply p-6 border-b border-border/30;
  }

  .sidebar-nav {
    @apply flex-1 overflow-y-auto p-4;
  }

  .sidebar-nav-item {
    @apply w-full flex items-center px-4 py-3 mb-2 rounded-xl transition-all duration-300 text-foreground;
  }

  .sidebar-nav-item:hover {
    @apply bg-primary/10 text-primary;
  }

  .sidebar-nav-item--active {
    @apply bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-lg;
  }
}

/* Button System - Using Theme Variables */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-xl font-medium text-sm transition-all duration-200 border-0 cursor-pointer no-underline;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground shadow-lg hover:-translate-y-0.5 hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground shadow-lg hover:-translate-y-0.5 hover:shadow-xl;
  }

  .btn-success {
    @apply bg-green-600 text-white shadow-lg hover:-translate-y-0.5 hover:shadow-xl hover:bg-green-700;
  }

  .btn-danger {
    @apply bg-destructive text-destructive-foreground shadow-lg hover:-translate-y-0.5 hover:shadow-xl;
  }

  .btn-glass {
    @apply backdrop-blur-md bg-card/80 border border-border/30 text-foreground hover:bg-card/90 hover:-translate-y-0.5;
  }
}

/* Card System - Using Theme Variables */
@layer components {
  .card {
    @apply backdrop-blur-md bg-card/80 border border-border/20 rounded-2xl p-6 shadow-glass transition-all duration-300;
  }

  .card:hover {
    @apply -translate-y-1 shadow-elevated;
  }
}

/* Status System - Using Theme Variables */
@layer components {
  .status-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border;
  }

  .status-success {
    @apply bg-green-100/80 text-green-800 border-green-200/50 dark:bg-green-900/20 dark:text-green-400 dark:border-green-700/50;
  }

  .status-warning {
    @apply bg-yellow-100/80 text-yellow-800 border-yellow-200/50 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-700/50;
  }

  .status-error {
    @apply bg-red-100/80 text-red-800 border-red-200/50 dark:bg-red-900/20 dark:text-red-400 dark:border-red-700/50;
  }

  .status-info {
    @apply bg-blue-100/80 text-blue-800 border-blue-200/50 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-700/50;
  }

  .status-neutral {
    @apply bg-muted text-muted-foreground border-border;
  }
}

/* Legacy Compatibility - Using Theme Variables */
@layer components {
  .macos-card {
    @apply backdrop-blur-md bg-card/80 border border-border/20 rounded-2xl p-6 shadow-glass;
  }

  .macos-button {
    @apply backdrop-blur-md bg-card/80 border border-border/30 text-foreground px-4 py-2 rounded-xl font-medium transition-all duration-200 cursor-pointer hover:bg-card/90 hover:-translate-y-0.5;
  }

  .macos-input {
    @apply backdrop-blur-md bg-input border border-border rounded-lg px-3 py-2 text-foreground transition-all duration-200 focus:outline-none focus:bg-card focus:border-ring focus:ring-2 focus:ring-ring/20;
  }

  .macos-text-primary {
    @apply text-foreground;
  }

  .macos-text-secondary {
    @apply text-muted-foreground;
  }

  .macos-text-tertiary {
    @apply text-muted-foreground/70;
  }

  .macos-accent-text {
    @apply text-primary;
  }
}

/* =================================
   ANIMATIONS
   ================================= */

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-slide-in {
  animation: slideInLeft 0.3s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}
