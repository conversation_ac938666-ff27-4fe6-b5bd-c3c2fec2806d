import React, {
    useState
  } from 'react';
  import {
    useDispatch, useSelector
  } from 'react-redux';
  import {
    useNavigate, Link
  } from 'react-router-dom';
  import {
    useTranslation
  } from 'react-i18next';
  import type {
    AppDispatch, RootState
  } from '../../store';
  import {
    login, clearError
  } from '../../store/slices/authSlice';
  import {
    Button
  } from '../ui/Button';
  import FormField from '../../shared/components/forms/FormField';
  import {
    Card, CardContent, CardDescription, CardHeader, CardTitle
  } from '../ui/card';
  import LanguageSwitcher from '../ui/LanguageSwitcher';
  import {
    useTheme
  } from '../../hooks/useTheme';
  import type {
    LoginCredentials
  } from '../../types/auth';
  import {
    LogIn, ArrowLeft, Hospital, Users, Mail, Lock
  } from 'lucide-react';
  const LoginForm: React.FC = () => {
    const dispatch = useDispatch
    <AppDispatch>();
  const navigate =
  useNavigate();
  const {
    isLoading, error
  } =
  useSelector((state: RootState) => state.auth);
  const {
    t
  } =
  useTranslation();
  const {
    isDark
  } =
  useTheme();
  const [formData, setFormData] = useState
    <LoginCredentials>({
    username: '', password: '',
  }); // Test users for easy selection

const testUsers = [ {
    username: 'admin', password: 'admin123', role: 'Administrator', description: '👑 Admin (Full Access)'
  }, {
    username: 'dr.smith', password: 'doctor123', role: 'Doctor', description: '🩺 Dr. Smith (Doctor)'
  }, {
    username: 'nurse.mary', password: 'nurse123', role: 'Nurse', description: '👩‍⚕️ Nurse Mary (Nurse)'
  }, {
    username: 'receptionist', password: 'reception123', role: 'Receptionist', description: '🏥 Receptionist (Front Desk)'
  }, {
    username: 'patient.doe', password: 'patient123', role: 'Patient', description: '🤒 John Doe (Patient)'
  }, ];
  const handleTestUserSelect = (testUser: typeof testUsers[0]) => {
    setFormData({
    username: testUser.username, password: testUser.password,
  }); // Clear any existing errors
  if (error) {
    dispatch(clearError());
  }
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const {
    name, value
  } = e.target;
  setFormData(prev => ({ ...prev, [name]: value,
  })); // Clear error when user starts typing
  if (error) {
    dispatch(clearError());
  }
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
  try {
    const result = await dispatch(login(formData));
  if (login.fulfilled.match(result)) {
    navigate('/dashboard');
  }
  } catch (error) {
    console.error('Login failed:', error);
  }
  };
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 relative overflow-hidden"> {/* Background Pattern */
  } <div className="absolute inset-0 opacity-30"> <div className="absolute inset-0" style={{
    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23007AFF' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
  }
  }></div> </div> {/* Header Controls */
  } <div className="absolute top-4 right-4 flex items-center gap-3 z-10">
    <LanguageSwitcher /> </div> {/* Back to Home */
  } <div className="absolute top-4 left-4 z-10">
    <Link to="/" className="macos-button flex items-center gap-2 px-4 py-2 text-sm font-medium macos-transition hover:scale-105" >
    <ArrowLeft className="w-4 h-4" /> {
    t('common.backToHome')
  }
    </Link> </div> <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8"> <div className="max-w-md w-full space-y-8">
    <Card variant="glass" className="shadow-2xl">
    <CardHeader className="text-center space-y-4"> <div className="mx-auto w-16 h-16 macos-accent-bg rounded-2xl flex items-center justify-center shadow-lg">
    <Hospital className="w-8 h-8 text-white" /> </div>
    <CardTitle className="text-3xl font-bold macos-text-primary"> {
    t('auth.welcomeBack')
  }
    </CardTitle>
    <CardDescription className="text-lg macos-text-secondary"> {
    t('auth.loginToAccount')
  }
    </CardDescription>
    </CardHeader>
    <CardContent className="space-y-6"> {/* Test User Selector */
  } <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 border /50 /50"> <div className="flex items-center gap-2 mb-3">
    <Users className="w-4 h-4 text-sky-700 dark:text-sky-400 dark:text-blue-400" /> <span className="text-sm font-semibold text-sky-700 dark:text-sky-400 dark:text-blue-300"> Quick Test Login </span> </div> <div className="grid grid-cols-1 gap-2"> {
    testUsers.map((user, index) => ( <button key={
    index
  } type="button" onClick={() => handleTestUserSelect(user)
  } className="text-left p-3 rounded-lg bg-background/50 dark:bg-gray-800/50 hover:bg-background/80 dark:hover:bg-gray-800/80 border /30 dark:border-blue-700/30 transition-all duration-200 hover:scale-[1.02]" > <div className="text-sm font-medium text-blue-900 dark:text-blue-100"> {
    user.description
  } </div> <div className="text-xs text-sky-700 dark:text-sky-400 dark:text-blue-400 mt-1"> {
    user.username
  } • {
    user.role
  } </div> </button> ))
  } </div> <div className="text-xs text-sky-700 dark:text-sky-400 dark:text-blue-400 mt-2 text-center"> Click any user above to auto-fill login credentials </div> </div> <form onSubmit={
    handleSubmit
  } className="space-y-6"> <div className="space-y-4">
    <FormField name="username" label={
    t('auth.email')
  } type="text" value={
    formData.username
  } onChange={(value) => setFormData(prev => ({ ...prev, username: value
  }))
  } placeholder={
    t('auth.emailPlaceholder')
  } required icon={
    Mail
  } variant="glass" className="h-12 text-base" />
    <FormField name="password" label={
    t('auth.password')
  } type="password" value={
    formData.password
  } onChange={(value) => setFormData(prev => ({ ...prev, password: value
  }))
  } placeholder={
    t('auth.passwordPlaceholder')
  } required icon={
    Lock
  } variant="glass" className="h-12 text-base" showPasswordToggle /> </div> {
    error && ( <div className="glass-error rounded-lg p-3 text-center"> <p className="text-sm font-medium text-rose-700 dark:text-rose-400 dark:text-red-400"> {
    error
  } </p> </div> )
  }
    <Button type="submit" disabled={
    isLoading
  } className="w-full h-12 text-base font-semibold" variant="glass" > {
    isLoading ? ( <div className="flex items-center gap-2"> <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div> {
    t('common.loading')
  } </div> ) : ( <div className="flex items-center gap-2">
    <LogIn className="w-5 h-5" /> {
    t('auth.loginButton')
  } </div> )
  }
    </Button> <div className="text-center"> <p className="text-sm macos-text-secondary"> {
    t('auth.noAccount')
  }{' '
  }
    <Link to="/register" className="font-semibold macos-accent-text hover:opacity-80 macos-transition" > {
    t('auth.signUp')
  }
    </Link> </p> </div> </form>
    </CardContent>
    </Card> </div> </div> </div> );
  };
  export default LoginForm;
