import React, {
    useState
  } from 'react';
  import {
    Brain, MessageSquare, Stethoscope, Pill, Shield, TrendingUp, Bot, Activity, AlertTriangle, Users, Zap, Settings
  } from 'lucide-react';
  import {
    Card, CardContent, CardDescription, CardHeader, CardTitle
  } from '../components/ui/card';
  import {
    Button
  } from '../components/ui/Button';
  import {
    Badge
  } from '../components/ui/badge';
  import AIDashboard from '../components/ai/AIDashboard';
  import AIChat from '../components/ai/AIChat';
  import AIDiagnosis from '../components/ai/AIDiagnosis';
  import MultiAgentDashboard from '../components/ai/MultiAgentDashboard';
  import MultiAgentCaseProcessor from '../components/ai/MultiAgentCaseProcessor';
  import AIConfigManager from '../components/ai/AIConfigManager';
  import EmergencyActivation from '../components/ai/EmergencyActivation';
  type AIServiceTab = 'dashboard' | 'chat' | 'diagnosis' | 'treatment' | 'risk' | 'drugs' | 'multi-agent' | 'case-processor' | 'emergency' | 'config';
  const AIServices: React.FC = () => {
    const [activeTab, setActiveTab] = useState
    <AIServiceTab>('dashboard');
  const [selectedPatientId, setSelectedPatientId] = useState<number | undefined>();
  const tabs = [ {
    id: 'dashboard' as AIServiceTab, label: 'AI Dashboard', icon:
    <Activity className="w-4 h-4" />, description: 'Overview of AI services and metrics'
  }, {
    id: 'chat' as AIServiceTab, label: 'AI Assistant', icon:
    <MessageSquare className="w-4 h-4" />, description: 'Chat with AI medical assistant'
  }, {
    id: 'diagnosis' as AIServiceTab, label: 'AI Diagnosis', icon:
    <Stethoscope className="w-4 h-4" />, description: 'Generate AI-powered diagnoses'
  }, {
    id: 'treatment' as AIServiceTab, label: 'Treatment Plans', icon:
    <Brain className="w-4 h-4" />, description: 'AI treatment recommendations'
  }, {
    id: 'risk' as AIServiceTab, label: 'Risk Assessment', icon:
    <Shield className="w-4 h-4" />, description: 'Patient risk analysis'
  }, {
    id: 'drugs' as AIServiceTab, label: 'Drug Interactions', icon:
    <Pill className="w-4 h-4" />, description: 'Check medication interactions'
  }, {
    id: 'multi-agent' as AIServiceTab, label: 'Multi-Agent System', icon:
    <Activity className="w-4 h-4" />, description: 'Specialized AI agents working together'
  }, {
    id: 'case-processor' as AIServiceTab, label: 'Case Processor', icon:
    <TrendingUp className="w-4 h-4" />, description: 'Process cases through multi-agent workflow'
  }, {
    id: 'emergency' as AIServiceTab, label: 'Emergency Protocols', icon:
    <AlertTriangle className="w-4 h-4" />, description: 'Rapid emergency response activation'
  }, {
    id: 'config' as AIServiceTab, label: 'AI Configuration', icon:
    <Settings className="w-4 h-4" />, description: 'Manage AI models, agents, and workflows'
  } ];
  const renderTabContent = () => {
    switch (activeTab) {
    case 'dashboard': return
    <AIDashboard />;
  case 'chat':
  return (
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <MessageSquare className="w-5 h-5 text-primary" /> AI Medical Assistant
    </CardTitle>
    <CardDescription> Chat with our AI assistant for medical consultations and general health queries
    </CardDescription>
    </CardHeader>
    <CardContent className="p-0"> <div className="h-[600px]">
    <AIChat patientId={
    selectedPatientId
  } conversationType="general" className="border-0 rounded-none" /> </div>
    </CardContent>
    </Card> );
  case 'diagnosis': return selectedPatientId ? (
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Stethoscope className="w-5 h-5 macos-accent-text" /> AI Diagnosis Generator
    </CardTitle>
    <CardDescription> Generate AI-powered diagnostic suggestions based on patient symptoms
    </CardDescription>
    </CardHeader>
    <CardContent>
    <AIDiagnosis patientId={
    selectedPatientId
  } onDiagnosisGenerated={(diagnosis) => { // Handle diagnosis result
  }
  } />
    </CardContent>
    </Card> ) : (
    <Card className="macos-card">
    <CardContent className="p-12 text-center">
    <Stethoscope className="w-16 h-16 mx-auto text-muted-foreground mb-4" /> <h3 className="text-lg font-medium text-foreground mb-2"> Select a Patient </h3> <p className="text-muted-foreground"> Please select a patient to generate AI diagnosis </p>
    </CardContent>
    </Card> );
  case 'treatment':
  return (
    <Card className="macos-card">
    <CardContent className="p-12 text-center">
    <Brain className="w-16 h-16 mx-auto macos-text-tertiary mb-4" /> <h3 className="text-lg font-medium macos-text-primary mb-2"> AI Treatment Plans </h3> <p className="macos-text-secondary mb-6"> Generate personalized treatment recommendations using AI </p>
    <Button className="macos-button"> Coming Soon
    </Button>
    </CardContent>
    </Card> );
  case 'risk':
  return (
    <Card className="macos-card">
    <CardContent className="p-12 text-center">
    <Shield className="w-16 h-16 mx-auto macos-text-tertiary mb-4" /> <h3 className="text-lg font-medium macos-text-primary mb-2"> Risk Assessment </h3> <p className="macos-text-secondary mb-6"> AI-powered patient risk analysis and predictions </p>
    <Button className="macos-button"> Coming Soon
    </Button>
    </CardContent>
    </Card> );
  case 'drugs':
  return (
    <Card className="macos-card">
    <CardContent className="p-12 text-center">
    <Pill className="w-16 h-16 mx-auto macos-text-tertiary mb-4" /> <h3 className="text-lg font-medium macos-text-primary mb-2"> Drug Interaction Checker </h3> <p className="macos-text-secondary mb-6"> AI-powered medication safety and interaction analysis </p>
    <Button className="macos-button"> Coming Soon
    </Button>
    </CardContent>
    </Card> );
  case 'multi-agent':
  return (
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Users className="w-5 h-5 macos-accent-text" /> Multi-Agent System
    </CardTitle>
    <CardDescription> Collaborative AI agents working together for complex medical cases
    </CardDescription>
    </CardHeader>
    <CardContent>
    <MultiAgentDashboard />
    </CardContent>
    </Card> );
  case 'case-processor':
  return (
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Activity className="w-5 h-5 macos-accent-text" /> Case Processor
    </CardTitle>
    <CardDescription> Advanced AI case analysis and processing system
    </CardDescription>
    </CardHeader>
    <CardContent>
    <MultiAgentCaseProcessor />
    </CardContent>
    </Card> );
  case 'emergency':
  return (
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <AlertTriangle className="w-5 h-5 text-red-500" /> Emergency Activation
    </CardTitle>
    <CardDescription> Emergency response system with AI-powered triage
    </CardDescription>
    </CardHeader>
    <CardContent>
    <EmergencyActivation />
    </CardContent>
    </Card> );
  case 'config':
  return (
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Settings className="w-5 h-5 macos-accent-text" /> AI Configuration Manager
    </CardTitle>
    <CardDescription> Centralized configuration for AI models, agents, and workflows
    </CardDescription>
    </CardHeader>
    <CardContent>
    <AIConfigManager />
    </CardContent>
    </Card> );
  default:
  return (
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Brain className="w-5 h-5 macos-accent-text" /> AI Dashboard
    </CardTitle>
    <CardDescription> Overview of all AI services and analytics
    </CardDescription>
    </CardHeader>
    <CardContent>
    <AIDashboard />
    </CardContent>
    </Card> );
  }
  };
  return ( <div className="min-h-screen bg-background"> <div className="max-w-7xl mx-auto p-6"> {/* Header */
  } <div className="mb-8"> <div className="flex items-center gap-4 mb-6"> <div className="p-3 bg-primary rounded-xl text-primary-foreground shadow-lg">
    <Bot className="w-8 h-8" /> </div> <div> <h1 className="text-3xl font-bold text-foreground"> AI Medical Services </h1> <p className="text-muted-foreground"> Powered by LangGraph + Gemini AI + Custom ML Models </p> </div> </div> {/* Patient Selector */
  }
    <Card className="macos-card mb-6">
    <CardContent className="p-4"> <div className="flex items-center gap-4">
    <Users className="w-5 h-5 macos-accent-text" /> <label className="text-sm font-medium macos-text-primary"> Select Patient: </label> <select value={
    selectedPatientId || ''
  } onChange={(e) => setSelectedPatientId(e.target.value ? Number(e.target.value) : undefined)
  } className="macos-input flex-1 max-w-xs" > <option value="">Select a patient...</option> <option value="1">John Doe (P000001)</option> <option value="2">Jane Smith (P000002)</option> <option value="3">Mike Johnson (P000003)</option> </select> </div>
    </CardContent>
    </Card> {/* Technology Stack Info */
  } <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <Card className="macos-card">
    <CardContent className="p-4"> <div className="flex items-center gap-3"> <div className="p-2 feature-blue rounded-lg">
    <Brain className="w-5 h-5" /> </div> <div> <h3 className="font-medium macos-text-primary">Gemini AI</h3> <p className="text-sm macos-text-secondary">Natural Language Processing</p>
    <Badge className="badge-success mt-1">Active
    </Badge> </div> </div>
    </CardContent>
    </Card>
    <Card className="macos-card">
    <CardContent className="p-4"> <div className="flex items-center gap-3"> <div className="p-2 feature-green rounded-lg">
    <TrendingUp className="w-5 h-5" /> </div> <div> <h3 className="font-medium macos-text-primary">LangGraph</h3> <p className="text-sm macos-text-secondary">Workflow Orchestration</p>
    <Badge className="badge-success mt-1">Active
    </Badge> </div> </div>
    </CardContent>
    </Card>
    <Card className="macos-card">
    <CardContent className="p-4"> <div className="flex items-center gap-3"> <div className="p-2 feature-purple rounded-lg">
    <Activity className="w-5 h-5" /> </div> <div> <h3 className="font-medium macos-text-primary">Custom ML</h3> <p className="text-sm macos-text-secondary">Predictive Analytics</p>
    <Badge className="badge-success mt-1">Active
    </Badge> </div> </div>
    </CardContent>
    </Card> </div> </div> {/* Navigation Tabs */
  }
    <Card className="macos-card mb-8">
    <CardContent className="p-0"> <nav className="flex space-x-1 p-2 overflow-x-auto"> {
    tabs.map((tab) => (
    <Button key={
    tab.id
  } onClick={() => setActiveTab(tab.id)
  } variant={
    activeTab === tab.id ? "default" : "ghost"
  } className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-sm whitespace-nowrap macos-transition ${
    activeTab === tab.id ? 'macos-accent-bg text-white shadow-md' : 'macos-text-secondary hover:macos-text-primary hover:bg-accent'
  }`
  } > {
    tab.icon
  } <span>{
    tab.label
  }</span>
    </Button> ))
  } </nav>
    </CardContent>
    </Card> {/* Tab Content */
  } <div className="mb-8"> {
    renderTabContent()
  } </div> {/* Footer */
  }
    <Card className="macos-card">
    <CardContent className="p-6 text-center"> <div className="flex items-center justify-center gap-2 mb-2">
    <Zap className="w-5 h-5 macos-accent-text" /> <span className="font-medium macos-text-primary">AI-Powered Healthcare</span> </div> <p className="text-sm macos-text-secondary"> AI services are powered by advanced machine learning models and should be used as assistive tools alongside professional medical judgment. </p>
    </CardContent>
    </Card> </div> </div> );
  };
  export default AIServices;
