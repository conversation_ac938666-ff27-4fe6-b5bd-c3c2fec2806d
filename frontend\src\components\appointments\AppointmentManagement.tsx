import React, {
    useState, useEffect
  } from 'react';
  import {
    useSelector
  } from 'react-redux';
  import type {
    RootState
  } from '../../store';
  import appointmentService, {
    Appointment
  } from '../../services/appointmentService';
  import {
    Button
  } from '../ui/Button';
  const AppointmentManagement: React.FC = () => {
    const {
    user
  } =
  useSelector((state: RootState) => state.auth);
  const [appointments, setAppointments] = useState
    <Appointment[]>([]);
  const [loading, setLoading] =
  useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAppointment, setSelectedAppointment] = useState
    <Appointment | null>(null);
  const [showCreateForm, setShowCreateForm] =
  useState(false);
  const [filter, setFilter] =
  useState('all');
  useEffect(() => {
    loadAppointments();
  }, [filter]);
  const loadAppointments = async () => {
    try {
    setLoading(true);
  let data;
  switch (filter) {
    case 'today': data = await appointmentService.getTodayAppointments();
  break;
  case 'upcoming': data = await appointmentService.getUpcomingAppointments();
  break;
  default: data = await appointmentService.getAppointments();
  } setAppointments(data.results || data);
  setError(null);
  } catch (err) {
    setError('Failed to load appointments');
  console.error('Error loading appointments:', err);
  } finally {
    setLoading(false);
  }
  };
  const handleStatusChange = async (appointmentId: number, action: string) => {
    try {
    switch (action) {
    case 'confirm': await appointmentService.confirmAppointment(appointmentId);
  break;
  case 'cancel': await appointmentService.cancelAppointment(appointmentId);
  break;
  case 'complete': await appointmentService.completeAppointment(appointmentId);
  break;
  case 'no_show': await appointmentService.markNoShow(appointmentId);
  break;
  } await loadAppointments();
  } catch (err) {
    setError(`Failed to ${
    action
  } appointment`);
  console.error(`Error ${
    action
  } appointment:`, err);
  }
  };
  const getStatusColor = (status: string) => {
    switch (status) {
    case 'scheduled': return 'status-info';
  case 'confirmed': return 'status-success';
  case 'in_progress': return 'status-warning';
  case 'completed': return 'bg-muted text-foreground';
  case 'cancelled': return 'status-error';
  case 'no_show': return 'bg-orange-100 text-orange-800';
  default: return 'bg-muted text-foreground';
  }
  };
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };
  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${
    timeString
  }`).toLocaleTimeString([], {
    hour: '2-digit', minute: '2-digit'
  });
  };
  if (loading) {
    return ( <div className="flex justify-center items-center h-64"> <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div> </div> );
  }
  return ( <div className="space-y-6"> {/* Header */
  } <div className="flex justify-between items-center"> <h1 className="text-2xl font-bold text-foreground">Appointment Management</h1>
    <Button onClick={() => setShowCreateForm(true)
  } className="bg-blue-600 hover:bg-blue-700 text-white" > Schedule New Appointment
    </Button> </div> {/* Filters */
  } <div className="flex space-x-4">
    <Button onClick={() => setFilter('all')
  } variant={
    filter === 'all' ? 'default' : 'outline'
  } > All Appointments
    </Button>
    <Button onClick={() => setFilter('today')
  } variant={
    filter === 'today' ? 'default' : 'outline'
  } > Today
    </Button>
    <Button onClick={() => setFilter('upcoming')
  } variant={
    filter === 'upcoming' ? 'default' : 'outline'
  } > Upcoming
    </Button> </div> {/* Error Message */
  } {
    error && ( <div className="bg-red-100 border border-red-400 text-rose-700 dark:text-rose-400 px-4 py-3 rounded"> {
    error
  } </div> )
  } {/* Appointments List */
  } <div className="bg-background shadow overflow-hidden sm:rounded-md"> <ul className="divide-y divide-gray-200"> {
    appointments.length === 0 ? ( <li className="px-6 py-4 text-center text-gray-500"> No appointments found </li> ) : ( appointments.map((appointment) => ( <li key={
    appointment.id
  } className="px-6 py-4 hover:bg-muted"> <div className="flex items-center justify-between"> <div className="flex-1"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-foreground"> {
    appointment.appointment_id
  } </p> <p className="text-sm text-muted-foreground"> Patient: {
    appointment.patient.user.full_name
  } </p> <p className="text-sm text-muted-foreground"> Doctor: {
    appointment.doctor.full_name
  } </p> </div> <div className="text-right"> <p className="text-sm font-medium text-foreground"> {
    formatDate(appointment.appointment_date)
  } at {
    formatTime(appointment.appointment_time)
  } </p> <p className="text-sm text-muted-foreground"> {
    appointment.duration_minutes
  } minutes </p> <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
    getStatusColor(appointment.status)
  }`
  }> {
    appointment.status.replace('_', ' ').toUpperCase()
  } </span> </div> </div> <div className="mt-2"> <p className="text-sm text-muted-foreground"> <span className="font-medium">Type:</span> {
    appointment.appointment_type
  } </p> <p className="text-sm text-muted-foreground"> <span className="font-medium">Reason:</span> {
    appointment.reason_for_visit
  } </p> {
    appointment.notes && ( <p className="text-sm text-muted-foreground"> <span className="font-medium">Notes:</span> {
    appointment.notes
  } </p> )
  } </div> </div> {/* Action Buttons */
  } <div className="ml-4 flex space-x-2"> {
    appointment.status === 'scheduled' && ( <>
    <Button size="sm" onClick={() => handleStatusChange(appointment.id, 'confirm')
  } className="bg-green-600 hover:bg-green-700 text-white" > Confirm
    </Button>
    <Button size="sm" variant="outline" onClick={() => handleStatusChange(appointment.id, 'cancel')
  } > Cancel
    </Button> </> )
  } {
    appointment.status === 'confirmed' && ( <>
    <Button size="sm" onClick={() => handleStatusChange(appointment.id, 'complete')
  } className="bg-blue-600 hover:bg-blue-700 text-white" > Complete
    </Button>
    <Button size="sm" variant="outline" onClick={() => handleStatusChange(appointment.id, 'no_show')
  } > No Show
    </Button> </> )
  }
    <Button size="sm" variant="outline" onClick={() => setSelectedAppointment(appointment)
  } > View Details
    </Button> </div> </div> </li> )) )
  } </ul> </div> {/* Appointment Details Modal */
  } {
    selectedAppointment && ( <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"> <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-background"> <div className="mt-3"> <h3 className="text-lg font-medium text-foreground mb-4"> Appointment Details </h3> <div className="space-y-3"> <div> <span className="font-medium">ID:</span> {
    selectedAppointment.appointment_id
  } </div> <div> <span className="font-medium">Patient:</span> {
    selectedAppointment.patient.user.full_name
  } </div> <div> <span className="font-medium">Doctor:</span> {
    selectedAppointment.doctor.full_name
  } </div> <div> <span className="font-medium">Date:</span> {
    formatDate(selectedAppointment.appointment_date)
  } </div> <div> <span className="font-medium">Time:</span> {
    formatTime(selectedAppointment.appointment_time)
  } </div> <div> <span className="font-medium">Duration:</span> {
    selectedAppointment.duration_minutes
  } minutes </div> <div> <span className="font-medium">Type:</span> {
    selectedAppointment.appointment_type
  } </div> <div> <span className="font-medium">Status:</span> {
    selectedAppointment.status
  } </div> <div> <span className="font-medium">Reason:</span> {
    selectedAppointment.reason_for_visit
  } </div> {
    selectedAppointment.notes && ( <div> <span className="font-medium">Notes:</span> {
    selectedAppointment.notes
  } </div> )
  } <div> <span className="font-medium">Fee:</span> ${
    selectedAppointment.consultation_fee
  } </div> </div> <div className="mt-6 flex justify-end">
    <Button onClick={() => setSelectedAppointment(null)
  } variant="outline" > Close
    </Button> </div> </div> </div> </div> )
  } </div> );
  };
  export default AppointmentManagement;
