const fs = require('fs');
const path = require('path');

// Function to properly format React/TypeScript files
function formatReactFile(content) {
  // Simple but effective formatting approach
  let formatted = content;
  
  // Add line breaks after imports
  formatted = formatted.replace(/(import\s+[^;]+;)\s*/g, '$1\n');
  
  // Add line breaks after export statements
  formatted = formatted.replace(/(export\s+[^;{]+[;{])\s*/g, '$1\n');
  
  // Add line breaks before const/let/var declarations
  formatted = formatted.replace(/\s+(const|let|var)\s+/g, '\n\n$1 ');
  
  // Add line breaks before function declarations
  formatted = formatted.replace(/\s+(function\s+[^{]+{)/g, '\n\n$1');
  
  // Add line breaks before return statements
  formatted = formatted.replace(/\s+(return\s*\()/g, '\n  $1');
  
  // Add line breaks before React hooks
  formatted = formatted.replace(/\s+(use[A-Z][a-zA-Z]*\s*\()/g, '\n  $1');
  
  // Add line breaks before if statements
  formatted = formatted.replace(/\s+(if\s*\()/g, '\n  $1');
  
  // Add line breaks after opening braces
  formatted = formatted.replace(/{\s*(?=[a-zA-Z_$<])/g, '{\n    ');
  
  // Add line breaks before closing braces
  formatted = formatted.replace(/\s*}/g, '\n  }');
  
  // Add line breaks around JSX elements
  formatted = formatted.replace(/\s*(<[A-Z][^>]*>)/g, '\n    $1');
  formatted = formatted.replace(/\s*(<\/[A-Z][^>]*>)/g, '\n    $1');
  
  // Add line breaks after semicolons (but be careful with for loops)
  formatted = formatted.replace(/;\s*(?=[a-zA-Z_$])/g, ';\n  ');
  
  // Clean up multiple consecutive newlines
  formatted = formatted.replace(/\n\s*\n\s*\n+/g, '\n\n');
  
  // Clean up leading/trailing whitespace
  formatted = formatted.trim();
  
  // Add final newline
  if (!formatted.endsWith('\n')) {
    formatted += '\n';
  }
  
  return formatted;
}

// Get all TypeScript/TSX files that need formatting
function getFilesToFix() {
  const files = [];
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && item !== 'node_modules' && !item.startsWith('.')) {
          scanDirectory(fullPath);
        } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
          try {
            const content = fs.readFileSync(fullPath, 'utf8');
            // Check if file is on a single line and is substantial
            if (!content.includes('\n') && content.length > 100) {
              files.push(fullPath);
            }
          } catch (readError) {
            console.warn(`Warning: Could not read ${fullPath}`);
          }
        }
      }
    } catch (dirError) {
      console.warn(`Warning: Could not scan directory ${dir}`);
    }
  }
  
  const srcPath = fs.existsSync('./src') ? './src' : './frontend/src';
  scanDirectory(srcPath);
  return files;
}

// Main function to fix all files
function fixAllFiles() {
  console.log('🔧 Starting to fix formatting issues...\n');
  
  const filesToFix = getFilesToFix();
  
  console.log(`Found ${filesToFix.length} files that need formatting...\n`);
  
  let fixedCount = 0;
  let errorCount = 0;
  
  for (const filePath of filesToFix) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const formatted = formatReactFile(content);
      
      if (formatted !== content) {
        fs.writeFileSync(filePath, formatted, 'utf8');
        const srcPath = fs.existsSync('./src') ? './src' : './frontend/src';
        console.log(`✅ Fixed: ${path.relative(srcPath, filePath)}`);
        fixedCount++;
      } else {
        const srcPath = fs.existsSync('./src') ? './src' : './frontend/src';
        console.log(`⚪ No changes needed: ${path.relative(srcPath, filePath)}`);
      }
    } catch (error) {
      const srcPath = fs.existsSync('./src') ? './src' : './frontend/src';
      console.error(`❌ Error fixing ${path.relative(srcPath, filePath)}:`, error.message);
      errorCount++;
    }
  }
  
  console.log(`\n🎉 Formatting complete!`);
  console.log(`✅ Successfully fixed: ${fixedCount} files`);
  if (errorCount > 0) {
    console.log(`❌ Errors encountered: ${errorCount} files`);
  }
  console.log('\nYou can now run your frontend application!');
}

// Run the script
if (require.main === module) {
  fixAllFiles();
}

module.exports = { formatReactFile, getFilesToFix };
