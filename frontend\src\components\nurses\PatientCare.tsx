import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/Button';
  import {
    Badge
  } from '../ui/badge';
  import {
    useTheme
  } from '../../hooks/useTheme';
  import {
    Users, Heart, Thermometer, Activity, Pill, Clock, AlertTriangle, CheckCircle, Plus, Edit, Eye, Bell
  } from 'lucide-react';
  import {
    getStatusClass, getPriorityClass
  } from '../../utils/styleUtils';
  const PatientCare: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const {
    isDark
  } =
  useTheme();
  const [activeTab, setActiveTab] =
  useState('assigned');
  const assignedPatients = [ {
    id: 1, name: '<PERSON>', room: '101', age: 45, condition: 'Post-surgery recovery', status: 'stable', lastVitals: {
    temperature: '98.6°F', bloodPressure: '120/80', heartRate: '72 bpm', oxygenSat: '98%', recordedAt: '10:30 AM'
  }, medications: [ {
    name: 'Morphine 5mg', time: '12:00 PM', status: 'due'
  }, {
    name: 'Antibiotics', time: '02:00 PM', status: 'given'
  } ], alerts: ['Pain level 6/10', 'Requested assistance'], notes: 'Patient recovering well from surgery. Minimal pain reported.'
  }, {
    id: 2, name: 'Michael Brown', room: '203', age: 62, condition: 'Cardiac monitoring', status: 'critical', lastVitals: {
    temperature: '99.2°F', bloodPressure: '140/90', heartRate: '88 bpm', oxygenSat: '95%', recordedAt: '11:15 AM'
  }, medications: [ {
    name: 'Beta blocker', time: '01:00 PM', status: 'due'
  }, {
    name: 'Blood thinner', time: '11:00 AM', status: 'given'
  } ], alerts: ['High blood pressure', 'Irregular heartbeat'], notes: 'Requires frequent monitoring. Doctor notified of BP elevation.'
  }, {
    id: 3, name: 'Emily Davis', room: '105', age: 28, condition: 'Asthma exacerbation', status: 'stable', lastVitals: {
    temperature: '98.4°F', bloodPressure: '110/70', heartRate: '68 bpm', oxygenSat: '97%', recordedAt: '09:45 AM'
  }, medications: [ {
    name: 'Albuterol inhaler', time: '12:30 PM', status: 'due'
  }, {
    name: 'Prednisone', time: '10:00 AM', status: 'given'
  } ], alerts: [], notes: 'Breathing improved. Continue current treatment plan.'
  } ];
  const tasks = [ {
    id: 1, patientId: 1, patientName: 'Sarah Johnson', room: '101', task: 'Administer pain medication', priority: 'high', dueTime: '12:00 PM', status: 'pending', type: 'medication'
  }, {
    id: 2, patientId: 2, patientName: 'Michael Brown', room: '203', task: 'Record vital signs', priority: 'high', dueTime: '12:15 PM', status: 'pending', type: 'vitals'
  }, {
    id: 3, patientId: 3, patientName: 'Emily Davis', room: '105', task: 'Breathing assessment', priority: 'medium', dueTime: '01:00 PM', status: 'pending', type: 'assessment'
  }, {
    id: 4, patientId: 1, patientName: 'Sarah Johnson', room: '101', task: 'Wound dressing change', priority: 'medium', dueTime: '02:30 PM', status: 'pending', type: 'care'
  } ];
  const getTaskIcon = (type: string) => {
    switch (type) {
    case 'medication': return
    <Pill className="w-4 h-4" />;
  case 'vitals': return
    <Activity className="w-4 h-4" />;
  case 'assessment': return
    <Eye className="w-4 h-4" />;
  case 'care': return
    <Heart className="w-4 h-4" />;
  default: return
    <CheckCircle className="w-4 h-4" />;
  }
  };
  const stats = {
    totalPatients: assignedPatients.length, criticalPatients: assignedPatients.filter(p => p.status === 'critical').length, pendingTasks: tasks.filter(t => t.status === 'pending').length, medicationsDue: tasks.filter(t => t.type === 'medication' && t.status === 'pending').length
  };
  const renderPatientCard = (patient: any) => (
    <Card key={
    patient.id
  } className="mb-4">
    <CardContent className="p-6"> <div className="flex items-start justify-between mb-4"> <div className="flex-1"> <div className="flex items-center justify-between mb-3"> <div className="flex items-center space-x-3"> <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"> <span className="text-sky-700 dark:text-sky-400 font-semibold">{
    patient.room
  }</span> </div> <div> <h3 className="text-lg font-semibold text-foreground">{
    patient.name
  }</h3> <p className="text-sm text-muted-foreground">{
    patient.age
  } years • {
    patient.condition
  }</p> </div> </div>
    <Badge className={
    getStatusClass(patient.status)
  }> {
    patient.status
  }
    </Badge> </div> {/* Vital Signs */
  } <div className="bg-muted rounded-lg p-4 mb-4"> <h4 className="font-medium text-foreground mb-2">Latest Vitals ({
    patient.lastVitals.recordedAt
  })</h4> <div className="grid grid-cols-2 md:grid-cols-4 gap-3"> <div className="text-center">
    <Thermometer className="w-4 h-4 text-red-500 mx-auto mb-1" /> <p className="text-xs text-muted-foreground">Temp</p> <p className="font-semibold text-sm">{
    patient.lastVitals.temperature
  }</p> </div> <div className="text-center">
    <Activity className="w-4 h-4 text-blue-500 mx-auto mb-1" /> <p className="text-xs text-muted-foreground">BP</p> <p className="font-semibold text-sm">{
    patient.lastVitals.bloodPressure
  }</p> </div> <div className="text-center">
    <Heart className="w-4 h-4 text-green-500 mx-auto mb-1" /> <p className="text-xs text-muted-foreground">HR</p> <p className="font-semibold text-sm">{
    patient.lastVitals.heartRate
  }</p> </div> <div className="text-center">
    <Activity className="w-4 h-4 text-purple-500 mx-auto mb-1" /> <p className="text-xs text-muted-foreground">O2 Sat</p> <p className="font-semibold text-sm">{
    patient.lastVitals.oxygenSat
  }</p> </div> </div> </div> {/* Medications */
  } <div className="mb-4"> <h4 className="font-medium text-foreground mb-2">Medications</h4> <div className="space-y-2"> {
    patient.medications.map((med: any, index: number) => ( <div key={
    index
  } className="flex items-center justify-between p-2 bg-background border rounded"> <div className="flex items-center space-x-2">
    <Pill className="w-4 h-4 text-sky-700 dark:text-sky-400" /> <span className="text-sm font-medium">{
    med.name
  }</span> </div> <div className="flex items-center space-x-2"> <span className="text-xs text-muted-foreground">{
    med.time
  }</span>
    <Badge className={
    med.status === 'due' ? 'bg-orange-100 text-orange-800' : 'status-success'
  }> {
    med.status
  }
    </Badge> </div> </div> ))
  } </div> </div> {/* Alerts */
  } {
    patient.alerts.length > 0 && ( <div className="mb-4"> <h4 className="font-medium text-foreground mb-2">Alerts</h4> <div className="space-y-1"> {
    patient.alerts.map((alert: string, index: number) => ( <div key={
    index
  } className="flex items-center space-x-2 p-2 bg-red-50 border rounded">
    <AlertTriangle className="w-4 h-4 text-rose-700 dark:text-rose-400" /> <span className="text-sm text-rose-700 dark:text-rose-400">{
    alert
  }</span> </div> ))
  } </div> </div> )
  } {/* Notes */
  } <div className="bg-blue-50 border rounded-lg p-3"> <p className="text-sm text-sky-700 dark:text-sky-400">{
    patient.notes
  }</p> </div> </div> <div className="flex flex-col space-y-2 ml-4">
    <Button size="sm">
    <Thermometer className="w-4 h-4 mr-1" /> Record Vitals
    </Button>
    <Button variant="outline" size="sm">
    <Pill className="w-4 h-4 mr-1" /> Medications
    </Button>
    <Button variant="outline" size="sm">
    <Edit className="w-4 h-4 mr-1" /> Add Notes
    </Button>
    <Button variant="outline" size="sm">
    <Bell className="w-4 h-4 mr-1" /> Call Nurse
    </Button> </div> </div>
    </CardContent>
    </Card> );
  const renderTaskCard = (task: any) => (
    <Card key={
    task.id
  } className="mb-3">
    <CardContent className="p-4"> <div className="flex items-center justify-between"> <div className="flex items-center space-x-3"> <div className="p-2 bg-blue-100 rounded-full"> {
    getTaskIcon(task.type)
  } </div> <div> <h4 className="font-medium text-foreground">{
    task.task
  }</h4> <p className="text-sm text-muted-foreground">{
    task.patientName
  } - Room {
    task.room
  }</p> </div> </div> <div className="flex items-center space-x-2">
    <Badge className={
    getPriorityClass(task.priority)
  }> {
    task.priority
  }
    </Badge> <span className="text-sm text-muted-foreground">{
    task.dueTime
  }</span>
    <Button size="sm">
    <CheckCircle className="w-4 h-4 mr-1" /> Complete
    </Button> </div> </div>
    </CardContent>
    </Card> );
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  }
    <Card className="glass border-0 shadow-xl">
    <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-xl flex items-center justify-center shadow-lg">
    <Heart className="w-6 h-6 text-white" /> </div> <div>
    <CardTitle className="text-2xl font-bold macos-text-primary">Patient Care
    </CardTitle> <p className="macos-text-secondary">Monitor and care for your assigned patients</p> </div> </div>
    <Button variant="glass" className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Add Task
    </Button> </div>
    </CardHeader>
    </Card> {/* Statistics */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Assigned Patients</p> <p className="text-2xl font-bold macos-text-primary">{
    stats.totalPatients
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
    <Users className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Critical Patients</p> <p className="text-2xl font-bold text-rose-700 dark:text-rose-400 dark:text-red-400">{
    stats.criticalPatients
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
    <AlertTriangle className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Pending Tasks</p> <p className="text-2xl font-bold text-amber-700 dark:text-amber-400 dark:text-yellow-400">{
    stats.pendingTasks
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
    <Clock className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Medications Due</p> <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{
    stats.medicationsDue
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
    <Pill className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card> </div> {/* Critical Patients Alert */
  } {
    stats.criticalPatients > 0 && (
    <Card className="glass border-0 shadow-lg /50 bg-red-50/50 dark:bg-red-900/20">
    <CardContent className="p-4"> <div className="flex items-center space-x-2">
    <AlertTriangle className="w-5 h-5 text-rose-700 dark:text-rose-400 dark:text-red-400" /> <p className="text-rose-700 dark:text-rose-400 dark:text-red-300"> <strong>Attention:</strong> You have {
    stats.criticalPatients
  } patient(s) in critical condition requiring immediate attention. </p> </div>
    </CardContent>
    </Card> )
  } {/* Tabs */
  }
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-0"> <div className="border-b border-border/50 dark:border-gray-700/50"> <nav className="-mb-px flex space-x-8 px-6"> <button onClick={() => setActiveTab('assigned')
  } className={`py-4 px-1 border-b-2 font-medium text-sm macos-transition ${
    activeTab === 'assigned' ? 'border-blue-500 text-sky-700 dark:text-sky-400 dark:text-blue-400' : 'border-transparent macos-text-secondary hover:macos-text-primary hover:border-border dark:hover:border-gray-600'
  }`
  } > Assigned Patients ({
    assignedPatients.length
  }) </button> <button onClick={() => setActiveTab('tasks')
  } className={`py-4 px-1 border-b-2 font-medium text-sm macos-transition ${
    activeTab === 'tasks' ? 'border-blue-500 text-sky-700 dark:text-sky-400 dark:text-blue-400' : 'border-transparent macos-text-secondary hover:macos-text-primary hover:border-border dark:hover:border-gray-600'
  }`
  } > Pending Tasks ({
    stats.pendingTasks
  }) </button> </nav> </div>
    </CardContent>
    </Card> {/* Content */
  } <div className="space-y-4"> {
    activeTab === 'assigned' ? ( assignedPatients.map(renderPatientCard) ) : ( tasks.map(renderTaskCard) )
  } </div> </div> </div> );
  };
  export default PatientCare;
