#!/usr/bin/env python3
"""
Advanced HMS Bug Detector
Proactive bug detection and system health monitoring
"""

import os
import sys
import django
import json
import time
import psutil
import traceback
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add parent directory to path for Django imports
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hms.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import connection, transaction
from django.core.cache import cache
from django.conf import settings
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

class AdvancedBugDetector:
    """Advanced bug detection and system monitoring"""
    
    def __init__(self):
        self.client = APIClient()
        self.issues = []
        self.warnings = []
        self.performance_metrics = {}
        self.system_health = {}
        
    def run_comprehensive_scan(self):
        """Run complete system scan for potential issues"""
        print("🔍 ADVANCED BUG DETECTION SCAN")
        print("=" * 60)
        
        scan_results = {
            'timestamp': datetime.now().isoformat(),
            'scan_duration': 0,
            'categories': {}
        }
        
        start_time = time.time()
        
        # Run all detection categories
        categories = [
            ('Integration Health', self._scan_integration_health),
            ('Authentication Security', self._scan_authentication_security),
            ('API Performance', self._scan_api_performance),
            ('Database Health', self._scan_database_health),
            ('Memory & Resources', self._scan_memory_resources),
            ('Frontend Code Quality', self._scan_frontend_quality),
            ('Security Vulnerabilities', self._scan_security_vulnerabilities),
            ('Data Consistency', self._scan_data_consistency),
            ('Error Patterns', self._scan_error_patterns),
            ('Performance Bottlenecks', self._scan_performance_bottlenecks)
        ]
        
        for category_name, scan_function in categories:
            print(f"\n🔍 Scanning: {category_name}")
            try:
                category_results = scan_function()
                scan_results['categories'][category_name] = category_results
                
                # Print summary
                issues_count = len(category_results.get('issues', []))
                warnings_count = len(category_results.get('warnings', []))
                
                if issues_count == 0 and warnings_count == 0:
                    print(f"   ✅ No issues found")
                else:
                    if issues_count > 0:
                        print(f"   ❌ {issues_count} issues found")
                    if warnings_count > 0:
                        print(f"   ⚠️  {warnings_count} warnings found")
                        
            except Exception as e:
                print(f"   ❌ Scan failed: {e}")
                scan_results['categories'][category_name] = {
                    'status': 'error',
                    'error': str(e),
                    'issues': [],
                    'warnings': []
                }
        
        scan_results['scan_duration'] = time.time() - start_time
        
        # Generate summary report
        self._generate_scan_report(scan_results)
        
        return scan_results
    
    def _scan_integration_health(self):
        """Scan for integration health issues"""
        issues = []
        warnings = []
        
        # Check token storage consistency
        frontend_files = [
            'frontend/src/utils/api.ts',
            'frontend/src/store/slices/authSlice.ts',
            'frontend/src/services/aiService.ts',
            'frontend/src/components/common/ErrorBoundary.tsx'
        ]
        
        for file_path in frontend_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # Check for inconsistent token storage
                    if "localStorage.getItem('access_token')" in content:
                        issues.append(f"Inconsistent token storage in {file_path}")
                    
                    # Check for hardcoded URLs
                    if 'http://localhost:' in content or 'http://127.0.0.1:' in content:
                        warnings.append(f"Hardcoded URLs in {file_path}")
        
        # Check patient ID format consistency
        try:
            from patient_management.models import Patient
            patients = Patient.objects.all()
            
            invalid_formats = []
            for patient in patients:
                if not patient.patient_id.startswith('P') or len(patient.patient_id) != 7:
                    invalid_formats.append(patient.patient_id)
            
            if invalid_formats:
                issues.append(f"Invalid patient ID formats: {invalid_formats}")
                
        except Exception as e:
            issues.append(f"Patient ID check failed: {e}")
        
        return {
            'status': 'healthy' if not issues else 'issues_found',
            'issues': issues,
            'warnings': warnings
        }
    
    def _scan_authentication_security(self):
        """Scan for authentication and security issues"""
        issues = []
        warnings = []
        
        try:
            # Check JWT configuration
            if not hasattr(settings, 'SIMPLE_JWT'):
                issues.append("JWT configuration missing")
            else:
                jwt_settings = settings.SIMPLE_JWT
                
                # Check token expiration times
                access_lifetime = jwt_settings.get('ACCESS_TOKEN_LIFETIME', timedelta(minutes=5))
                if access_lifetime > timedelta(hours=24):
                    warnings.append("Access token lifetime too long (security risk)")
                
                # Check refresh token rotation
                if not jwt_settings.get('ROTATE_REFRESH_TOKENS', False):
                    warnings.append("Refresh token rotation disabled")
            
            # Test authentication flow
            test_user = User.objects.create_user(
                username='security_test_user',
                email='<EMAIL>',
                password='testpass123',
                role='admin'
            )
            
            # Test login
            response = self.client.post('/api/auth/login/', {
                'username': 'security_test_user',
                'password': 'testpass123'
            })
            
            if response.status_code != 200:
                issues.append(f"Authentication endpoint failing: {response.status_code}")
            else:
                auth_data = response.json()
                if 'tokens' not in auth_data:
                    issues.append("Authentication response missing tokens")
            
            # Test password strength requirements
            weak_password_response = self.client.post('/api/auth/register/', {
                'username': 'weak_test',
                'email': '<EMAIL>',
                'password': '123',
                'password_confirm': '123',
                'role': 'patient'
            })
            
            if weak_password_response.status_code == 201:
                warnings.append("Weak password accepted (security risk)")
            
            # Cleanup
            test_user.delete()
            
        except Exception as e:
            issues.append(f"Authentication security scan failed: {e}")
        
        return {
            'status': 'secure' if not issues else 'vulnerabilities_found',
            'issues': issues,
            'warnings': warnings
        }
    
    def _scan_api_performance(self):
        """Scan for API performance issues"""
        issues = []
        warnings = []
        performance_data = {}
        
        try:
            # Create test user
            test_user = User.objects.create_user(
                username='perf_test_user',
                email='<EMAIL>',
                password='testpass123',
                role='admin'
            )
            
            # Get token
            refresh = RefreshToken.for_user(test_user)
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
            
            # Test endpoint performance
            endpoints = [
                '/api/users/users/',
                '/api/patients/patients/',
                '/api/appointments/appointments/',
            ]
            
            for endpoint in endpoints:
                start_time = time.time()
                response = self.client.get(endpoint)
                end_time = time.time()
                
                response_time = end_time - start_time
                performance_data[endpoint] = response_time
                
                if response_time > 2.0:
                    issues.append(f"{endpoint} slow response: {response_time:.2f}s")
                elif response_time > 1.0:
                    warnings.append(f"{endpoint} moderate response: {response_time:.2f}s")
            
            # Test database query performance
            with connection.cursor() as cursor:
                start_time = time.time()
                cursor.execute("SELECT COUNT(*) FROM users_user")
                query_time = time.time() - start_time
                
                if query_time > 0.5:
                    warnings.append(f"Slow database query: {query_time:.2f}s")
            
            # Cleanup
            test_user.delete()
            
        except Exception as e:
            issues.append(f"Performance scan failed: {e}")
        
        return {
            'status': 'optimal' if not issues else 'performance_issues',
            'issues': issues,
            'warnings': warnings,
            'metrics': performance_data
        }
    
    def _scan_database_health(self):
        """Scan for database health issues"""
        issues = []
        warnings = []
        
        try:
            with connection.cursor() as cursor:
                # Check for orphaned records
                orphan_queries = [
                    ("Appointments with invalid patients", """
                        SELECT COUNT(*) FROM appointments a
                        WHERE NOT EXISTS (
                            SELECT 1 FROM patient_management_patient p
                            WHERE p.id = a.patient_id
                        )
                    """),
                    ("Medical records with invalid patients", """
                        SELECT COUNT(*) FROM patient_management_medicalrecord mr
                        WHERE NOT EXISTS (
                            SELECT 1 FROM patient_management_patient p
                            WHERE p.id = mr.patient_id
                        )
                    """)
                ]
                
                for description, query in orphan_queries:
                    try:
                        cursor.execute(query)
                        count = cursor.fetchone()[0]
                        if count > 0:
                            issues.append(f"{description}: {count} orphaned records")
                    except Exception as e:
                        warnings.append(f"Could not check {description}: {e}")
                
                # Check for duplicate patient IDs
                cursor.execute("""
                    SELECT patient_id, COUNT(*) as count
                    FROM patient_management_patient
                    GROUP BY patient_id
                    HAVING COUNT(*) > 1
                """)
                duplicates = cursor.fetchall()
                
                if duplicates:
                    issues.append(f"Duplicate patient IDs: {len(duplicates)} sets")
                
                # Check table sizes
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """)
                tables = cursor.fetchall()
                
                large_tables = []
                for (table_name,) in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        if count > 10000:
                            large_tables.append((table_name, count))
                    except:
                        pass
                
                if large_tables:
                    warnings.append(f"Large tables may need optimization: {large_tables}")
                    
        except Exception as e:
            issues.append(f"Database health scan failed: {e}")
        
        return {
            'status': 'healthy' if not issues else 'issues_found',
            'issues': issues,
            'warnings': warnings
        }
    
    def _scan_memory_resources(self):
        """Scan for memory and resource issues"""
        issues = []
        warnings = []
        metrics = {}
        
        try:
            # Get system memory info
            memory = psutil.virtual_memory()
            metrics['memory_percent'] = memory.percent
            metrics['memory_available_gb'] = memory.available / (1024**3)
            
            if memory.percent > 90:
                issues.append(f"High memory usage: {memory.percent}%")
            elif memory.percent > 75:
                warnings.append(f"Moderate memory usage: {memory.percent}%")
            
            # Get CPU info
            cpu_percent = psutil.cpu_percent(interval=1)
            metrics['cpu_percent'] = cpu_percent
            
            if cpu_percent > 90:
                issues.append(f"High CPU usage: {cpu_percent}%")
            elif cpu_percent > 75:
                warnings.append(f"Moderate CPU usage: {cpu_percent}%")
            
            # Check disk space
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            metrics['disk_percent'] = disk_percent
            
            if disk_percent > 90:
                issues.append(f"Low disk space: {disk_percent:.1f}% used")
            elif disk_percent > 80:
                warnings.append(f"Moderate disk usage: {disk_percent:.1f}% used")
                
        except Exception as e:
            warnings.append(f"Resource monitoring failed: {e}")
        
        return {
            'status': 'optimal' if not issues else 'resource_issues',
            'issues': issues,
            'warnings': warnings,
            'metrics': metrics
        }
    
    def _scan_frontend_quality(self):
        """Scan frontend code quality"""
        issues = []
        warnings = []
        
        frontend_dir = Path('frontend/src')
        if not frontend_dir.exists():
            issues.append("Frontend directory not found")
            return {'status': 'missing', 'issues': issues, 'warnings': warnings}
        
        # Check TypeScript files
        ts_files = list(frontend_dir.rglob('*.ts')) + list(frontend_dir.rglob('*.tsx'))
        
        any_type_files = []
        console_log_files = []
        large_files = []
        
        for file_path in ts_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    # Check file size
                    if len(lines) > 500:
                        large_files.append(f"{file_path.name} ({len(lines)} lines)")
                    
                    # Check for any types
                    if ': any' in content or 'any[]' in content:
                        any_type_files.append(file_path.name)
                    
                    # Check for console.log
                    if 'console.log(' in content:
                        console_log_files.append(file_path.name)
                        
            except Exception as e:
                warnings.append(f"Could not read {file_path}: {e}")
        
        if any_type_files:
            warnings.append(f"Files with 'any' types: {len(any_type_files)}")
        
        if console_log_files:
            warnings.append(f"Files with console.log: {len(console_log_files)}")
        
        if large_files:
            warnings.append(f"Large files (>500 lines): {len(large_files)}")
        
        return {
            'status': 'clean' if not issues and not warnings else 'quality_issues',
            'issues': issues,
            'warnings': warnings
        }
    
    def _scan_security_vulnerabilities(self):
        """Scan for security vulnerabilities"""
        issues = []
        warnings = []
        
        try:
            # Check Django settings
            if settings.DEBUG:
                warnings.append("DEBUG mode enabled (disable in production)")
            
            if not settings.SECRET_KEY or len(settings.SECRET_KEY) < 50:
                issues.append("Weak or missing SECRET_KEY")
            
            # Check for HTTPS settings
            if not getattr(settings, 'SECURE_SSL_REDIRECT', False):
                warnings.append("HTTPS redirect not enabled")
            
            # Check CORS settings
            if getattr(settings, 'CORS_ALLOW_ALL_ORIGINS', False):
                issues.append("CORS allows all origins (security risk)")
            
            # Check for exposed sensitive data
            sensitive_patterns = [
                'password', 'secret', 'key', 'token', 'api_key'
            ]
            
            for pattern in sensitive_patterns:
                # This is a simplified check - in production, use proper secret scanning
                pass
                
        except Exception as e:
            warnings.append(f"Security scan failed: {e}")
        
        return {
            'status': 'secure' if not issues else 'vulnerabilities_found',
            'issues': issues,
            'warnings': warnings
        }
    
    def _scan_data_consistency(self):
        """Scan for data consistency issues"""
        issues = []
        warnings = []
        
        try:
            # Check user-patient relationships
            users_with_patient_role = User.objects.filter(role='patient').count()
            actual_patients = 0
            
            try:
                from patient_management.models import Patient
                actual_patients = Patient.objects.count()
            except:
                pass
            
            if users_with_patient_role != actual_patients:
                warnings.append(f"User-patient count mismatch: {users_with_patient_role} users, {actual_patients} patients")
            
            # Check appointment consistency
            try:
                from appointment_system.models import Appointment
                appointments = Appointment.objects.all()
                
                future_appointments = appointments.filter(appointment_date__gt=datetime.now().date()).count()
                past_appointments = appointments.filter(appointment_date__lt=datetime.now().date()).count()
                
                if past_appointments > future_appointments * 10:
                    warnings.append("Many old appointments - consider archiving")
                    
            except Exception as e:
                warnings.append(f"Appointment consistency check failed: {e}")
                
        except Exception as e:
            issues.append(f"Data consistency scan failed: {e}")
        
        return {
            'status': 'consistent' if not issues else 'inconsistencies_found',
            'issues': issues,
            'warnings': warnings
        }
    
    def _scan_error_patterns(self):
        """Scan for common error patterns"""
        issues = []
        warnings = []
        
        # This would typically scan log files for error patterns
        # For now, we'll do basic checks
        
        try:
            # Check for common Django issues
            if not hasattr(settings, 'DATABASES'):
                issues.append("Database configuration missing")
            
            # Check for missing migrations
            from django.core.management import call_command
            from io import StringIO
            
            out = StringIO()
            try:
                call_command('showmigrations', '--plan', stdout=out)
                migrations_output = out.getvalue()
                
                if '[ ]' in migrations_output:
                    warnings.append("Unapplied migrations found")
                    
            except Exception as e:
                warnings.append(f"Migration check failed: {e}")
                
        except Exception as e:
            warnings.append(f"Error pattern scan failed: {e}")
        
        return {
            'status': 'clean' if not issues else 'error_patterns_found',
            'issues': issues,
            'warnings': warnings
        }
    
    def _scan_performance_bottlenecks(self):
        """Scan for performance bottlenecks"""
        issues = []
        warnings = []
        
        try:
            # Check for N+1 query patterns (simplified check)
            # In a real implementation, you'd use Django Debug Toolbar or similar
            
            # Check for missing database indexes
            with connection.cursor() as cursor:
                # This is SQLite specific - adapt for other databases
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND sql IS NOT NULL
                """)
                indexes = cursor.fetchall()
                
                if len(indexes) < 5:
                    warnings.append("Few database indexes found - may impact performance")
            
            # Check for large querysets without pagination
            # This would require more sophisticated analysis
            
        except Exception as e:
            warnings.append(f"Performance bottleneck scan failed: {e}")
        
        return {
            'status': 'optimal' if not issues else 'bottlenecks_found',
            'issues': issues,
            'warnings': warnings
        }
    
    def _generate_scan_report(self, scan_results):
        """Generate comprehensive scan report"""
        print("\n" + "=" * 60)
        print("🏥 ADVANCED BUG DETECTION REPORT")
        print("=" * 60)
        
        total_issues = 0
        total_warnings = 0
        
        for category, results in scan_results['categories'].items():
            issues_count = len(results.get('issues', []))
            warnings_count = len(results.get('warnings', []))
            
            total_issues += issues_count
            total_warnings += warnings_count
            
            if issues_count > 0 or warnings_count > 0:
                print(f"\n🔍 {category}")
                print("-" * 40)
                
                for issue in results.get('issues', []):
                    print(f"   ❌ ISSUE: {issue}")
                
                for warning in results.get('warnings', []):
                    print(f"   ⚠️  WARNING: {warning}")
        
        print(f"\n📊 SUMMARY")
        print("-" * 40)
        print(f"Scan Duration: {scan_results['scan_duration']:.2f} seconds")
        print(f"Total Issues: {total_issues}")
        print(f"Total Warnings: {total_warnings}")
        
        if total_issues == 0 and total_warnings == 0:
            print("\n🎉 NO ISSUES FOUND!")
            print("✅ Your HMS system is healthy!")
        else:
            print(f"\n🔧 RECOMMENDATIONS")
            print("-" * 40)
            if total_issues > 0:
                print(f"1. Address {total_issues} critical issues immediately")
            if total_warnings > 0:
                print(f"2. Review {total_warnings} warnings for optimization")
            print("3. Run integration tests after fixes")
            print("4. Monitor system health regularly")
        
        # Save detailed report
        report_file = f"advanced_scan_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(scan_results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")


def main():
    """Main function for advanced bug detection"""
    detector = AdvancedBugDetector()
    detector.run_comprehensive_scan()


if __name__ == '__main__':
    main()
