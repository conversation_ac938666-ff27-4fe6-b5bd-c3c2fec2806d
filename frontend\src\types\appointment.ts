import {
    User
  } from './auth';
  import {
    Patient
  } from './patient';
  export interface Appointment {
    id: number;
  appointment_id: string;
  patient: Patient;
  doctor: User; // Appointment details appointment_date: string;
  appointment_time: string;
  duration_minutes: number;
  appointment_type: 'consultation' | 'follow_up' | 'emergency' | 'routine_checkup'; // Status and notes status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  reason_for_visit: string;
  notes?: string; // Billing consultation_fee: number; // Metadata created_by: User;
  created_at: string;
  updated_at: string; // Computed properties appointment_datetime?: string;
  is_today?: boolean;
  is_upcoming?: boolean;
  } export interface AppointmentSlot {
    id: number;
  doctor: User;
  date: string;
  start_time: string;
  end_time: string;
  is_available: boolean;
  max_appointments: number;
  created_at: string;
  updated_at: string; // Computed properties current_appointments_count?: number;
  is_fully_booked?: boolean;
  } export interface RecurringAppointment {
    id: number;
  patient: Patient;
  doctor: User;
  created_by: User; // Pattern details pattern_type: 'daily' | 'weekly' | 'monthly' | 'custom';
  interval: number;
  days_of_week?: string;
  day_of_month?: number; // Appointment details appointment_time: string;
  duration_minutes: number;
  appointment_type: 'consultation' | 'follow_up' | 'emergency' | 'routine_checkup';
  reason_for_visit: string; // Schedule start_date: string;
  end_date?: string;
  max_occurrences?: number; // Status is_active: boolean;
  created_at: string;
  updated_at: string;
  } export interface AppointmentReminder {
    id: number;
  appointment: Appointment; // Reminder details reminder_type: 'email' | 'sms' | 'push' | 'call';
  remind_before_hours: number;
  scheduled_time: string; // Status is_sent: boolean;
  sent_at?: string;
  delivery_status: 'pending' | 'sent' | 'delivered' | 'failed';
  error_message?: string;
  created_at: string;
  } export interface WaitingListEntry {
    id: number;
  patient: Patient;
  doctor: User; // Preferences preferred_date?: string;
  preferred_time_start?: string;
  preferred_time_end?: string;
  appointment_type: 'consultation' | 'follow_up' | 'emergency' | 'routine_checkup';
  reason_for_visit: string; // Priority priority: 'low' | 'normal' | 'high' | 'urgent'; // Status status: 'active' | 'scheduled' | 'cancelled' | 'expired'; // Notifications notify_patient: boolean;
  notification_method: 'email' | 'sms' | 'phone' | 'all';
  created_at: string;
  updated_at: string;
  } // API Request Types export interface AppointmentCreateRequest {
    patient: number;
  doctor: number;
  appointment_date: string;
  appointment_time: string;
  duration_minutes?: number;
  appointment_type: string;
  reason_for_visit: string;
  notes?: string;
  consultation_fee?: number;
  } export interface AppointmentUpdateRequest extends Partial
    <AppointmentCreateRequest> {
    status?: string;
  } export interface AppointmentSlotCreateRequest {
    doctor: number;
  date: string;
  start_time: string;
  end_time: string;
  is_available?: boolean;
  max_appointments?: number;
  } export interface RecurringAppointmentCreateRequest {
    patient: number;
  doctor: number;
  pattern_type: string;
  interval: number;
  days_of_week?: string;
  day_of_month?: number;
  appointment_time: string;
  duration_minutes: number;
  appointment_type: string;
  reason_for_visit: string;
  start_date: string;
  end_date?: string;
  max_occurrences?: number;
  } export interface WaitingListCreateRequest {
    patient: number;
  doctor: number;
  preferred_date?: string;
  preferred_time_start?: string;
  preferred_time_end?: string;
  appointment_type: string;
  reason_for_visit: string;
  priority?: string;
  notify_patient?: boolean;
  notification_method?: string;
  } // API Response Types export interface AppointmentListResponse {
    count: number;
  next?: string;
  previous?: string;
  results: Appointment[];
  } export interface AppointmentStatsResponse {
    total_appointments: number;
  today_appointments: number;
  upcoming_appointments: number;
  completed_appointments: number;
  cancelled_appointments: number;
  no_show_appointments: number;
  appointments_by_status: Record<string, number>;
  appointments_by_type: Record<string, number>;
  monthly_stats: Array<{
    month: string;
  count: number;
  }>;
  } export interface AvailableSlotsResponse {
    date: string;
  doctor: User;
  available_slots: Array<{
    start_time: string;
  end_time: string;
  is_available: boolean;
  }>;
  }
