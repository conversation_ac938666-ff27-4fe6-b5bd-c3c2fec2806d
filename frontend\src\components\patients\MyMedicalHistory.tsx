import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    <PERSON>, CardContent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/Button';
  import {
    Badge
  } from '../ui/badge';
  import {
    CURRENT_PATIENT_DATA
  } from '../../shared/data/mockPatientData';
  import {
    FileText, Calendar, User, Heart, Pill, TestTube, Download, Eye, Filter, Search
  } from 'lucide-react';
  const MyMedicalHistory: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const [activeTab, setActiveTab] =
  useState('overview');
  const [searchTerm, setSearchTerm] =
  useState('');
  const medicalHistory = {
    overview: {
    totalVisits: CURRENT_PATIENT_DATA.totalVisits, lastVisit: CURRENT_PATIENT_DATA.lastVisit, chronicConditions: CURRENT_PATIENT_DATA.chronicConditions, allergies: CURRENT_PATIENT_DATA.allergies, bloodType: CURRENT_PATIENT_DATA.bloodType
  }, visits: CURRENT_PATIENT_DATA.medicalVisits, prescriptions: CURRENT_PATIENT_DATA.activePrescriptions, labResults: [ {
    id: 1, test: 'Complete Blood Count', date: '2024-12-08', result: 'Normal', doctor: 'Dr. <PERSON> <PERSON>', status: 'completed'
  }, {
    id: 2, test: 'HbA1c', date: '2024-11-10', result: '7.2%', doctor: 'Dr. Michael Brown', status: 'completed'
  }, {
    id: 3, test: 'Lipid Panel', date: '2024-10-15', result: 'Borderline High', doctor: 'Dr. Emily Davis', status: 'completed'
  } ]
  };
  const tabs = [ {
    id: 'overview', label: 'Overview', icon: Heart
  }, {
    id: 'visits', label: 'Medical Visits', icon: Calendar
  }, {
    id: 'prescriptions', label: 'Prescriptions', icon: Pill
  }, {
    id: 'lab-results', label: 'Lab Results', icon: TestTube
  } ];
  const getStatusColor = (status: string) => {
    switch (status) {
    case 'active': case 'completed': return 'status-success';
  case 'pending': return 'status-warning';
  case 'cancelled': return 'status-error';
  default: return 'bg-muted text-foreground';
  }
  };
  const renderOverview = () => ( <div className="space-y-6"> <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <Card>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Total Visits</p> <p className="text-2xl font-bold text-foreground">{
    medicalHistory.overview.totalVisits
  }</p> </div>
    <Calendar className="w-8 h-8 text-sky-700 dark:text-sky-400" /> </div>
    </CardContent>
    </Card>
    <Card>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Last Visit</p> <p className="text-lg font-semibold text-foreground">{
    medicalHistory.overview.lastVisit
  }</p> </div>
    <User className="w-8 h-8 text-emerald-700 dark:text-emerald-400" /> </div>
    </CardContent>
    </Card>
    <Card>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Blood Type</p> <p className="text-2xl font-bold text-foreground">{
    medicalHistory.overview.bloodType
  }</p> </div>
    <Heart className="w-8 h-8 text-rose-700 dark:text-rose-400" /> </div>
    </CardContent>
    </Card>
    <Card>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">Conditions</p> <p className="text-lg font-semibold text-foreground">{
    medicalHistory.overview.chronicConditions.length
  }</p> </div>
    <FileText className="w-8 h-8 text-purple-600" /> </div>
    </CardContent>
    </Card> </div> <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    <Card>
    <CardHeader>
    <CardTitle>Chronic Conditions
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="space-y-2"> {
    medicalHistory.overview.chronicConditions.map((condition, index) => ( <div key={
    index
  } className="flex items-center justify-between p-3 bg-muted rounded-lg"> <span className="font-medium">{
    condition
  }</span>
    <Badge className="bg-orange-100 text-orange-800">Ongoing
    </Badge> </div> ))
  } </div>
    </CardContent>
    </Card>
    <Card>
    <CardHeader>
    <CardTitle>Known Allergies
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="space-y-2"> {
    medicalHistory.overview.allergies.map((allergy, index) => ( <div key={
    index
  } className="flex items-center justify-between p-3 bg-red-50 rounded-lg"> <span className="font-medium">{
    allergy
  }</span>
    <Badge className="status-error">Allergy
    </Badge> </div> ))
  } </div>
    </CardContent>
    </Card> </div> </div> );
  const renderVisits = () => ( <div className="space-y-4"> {
    medicalHistory.visits.map((visit) => (
    <Card key={
    visit.id
  }>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div className="flex-1"> <div className="flex items-center justify-between mb-2"> <h3 className="text-lg font-semibold text-foreground">{
    visit.diagnosis
  }</h3>
    <Badge className={
    getStatusColor(visit.status)
  }>{
    visit.status
  }
    </Badge> </div> <p className="text-sm text-muted-foreground mb-1"> <strong>Doctor:</strong> {
    visit.doctor
  } • <strong>Department:</strong> {
    visit.department
  } </p> <p className="text-sm text-muted-foreground mb-2"> <strong>Date:</strong> {
    visit.date
  } </p> <p className="text-sm text-foreground">{
    visit.notes
  }</p> </div> <div className="flex space-x-2 ml-4">
    <Button variant="outline" size="sm">
    <Eye className="w-4 h-4 mr-1" /> View
    </Button>
    <Button variant="outline" size="sm">
    <Download className="w-4 h-4 mr-1" /> Download
    </Button> </div> </div>
    </CardContent>
    </Card> ))
  } </div> );
  const renderPrescriptions = () => ( <div className="space-y-4"> {
    medicalHistory.prescriptions.map((prescription) => (
    <Card key={
    prescription.id
  }>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div className="flex-1"> <div className="flex items-center justify-between mb-2"> <h3 className="text-lg font-semibold text-foreground">{
    prescription.medication
  }</h3>
    <Badge className={
    getStatusColor(prescription.status)
  }>{
    prescription.status
  }
    </Badge> </div> <p className="text-sm text-muted-foreground mb-1"> <strong>Prescribed by:</strong> {
    prescription.prescribedBy
  } </p> <p className="text-sm text-muted-foreground mb-1"> <strong>Date:</strong> {
    prescription.date
  } • <strong>Duration:</strong> {
    prescription.duration
  } </p> <p className="text-sm text-foreground">{
    prescription.instructions
  }</p> </div> <div className="flex space-x-2 ml-4">
    <Button variant="outline" size="sm">
    <Download className="w-4 h-4 mr-1" /> Download
    </Button> </div> </div>
    </CardContent>
    </Card> ))
  } </div> );
  const renderLabResults = () => ( <div className="space-y-4"> {
    medicalHistory.labResults.map((result) => (
    <Card key={
    result.id
  }>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div className="flex-1"> <div className="flex items-center justify-between mb-2"> <h3 className="text-lg font-semibold text-foreground">{
    result.test
  }</h3>
    <Badge className={
    getStatusColor(result.status)
  }>{
    result.status
  }
    </Badge> </div> <p className="text-sm text-muted-foreground mb-1"> <strong>Result:</strong> {
    result.result
  } </p> <p className="text-sm text-muted-foreground"> <strong>Doctor:</strong> {
    result.doctor
  } • <strong>Date:</strong> {
    result.date
  } </p> </div> <div className="flex space-x-2 ml-4">
    <Button variant="outline" size="sm">
    <Eye className="w-4 h-4 mr-1" /> View
    </Button>
    <Button variant="outline" size="sm">
    <Download className="w-4 h-4 mr-1" /> Download
    </Button> </div> </div>
    </CardContent>
    </Card> ))
  } </div> );
  const renderContent = () => {
    switch (activeTab) {
    case 'overview': return renderOverview();
  case 'visits': return renderVisits();
  case 'prescriptions': return renderPrescriptions();
  case 'lab-results': return renderLabResults();
  default: return renderOverview();
  }
  };
  return ( <div className="space-y-6 p-6"> {/* Header */
  } <div className="flex items-center justify-between"> <div> <h1 className="text-3xl font-bold text-foreground">My Medical History</h1> <p className="text-lg text-muted-foreground mt-2">Complete overview of your medical records</p> </div> <div className="flex space-x-2">
    <Button variant="outline">
    <Download className="w-4 h-4 mr-2" /> Export All
    </Button>
    <Button>
    <FileText className="w-4 h-4 mr-2" /> Request Records
    </Button> </div> </div> {/* Tabs */
  } <div className="border-b border-border"> <nav className="-mb-px flex space-x-8"> {
    tabs.map((tab) => {
    const IconComponent = tab.icon;
  return ( <button key={
    tab.id
  } onClick={() => setActiveTab(tab.id)
  } className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
    activeTab === tab.id ? 'border-blue-500 text-sky-700 dark:text-sky-400' : 'border-transparent text-gray-500 hover:text-foreground hover:border-border'
  }`
  } >
    <IconComponent className="w-4 h-4 mr-2" /> {
    tab.label
  } </button> );
  })
  } </nav> </div> {/* Content */
  } {
    renderContent()
  } </div> );
  };
  export default MyMedicalHistory;
