import React, {
    useState, useEffect
  } from 'react';
  import {
    X, Save, Loader2
  } from 'lucide-react';
  import {
    Button
  } from './Button';
  import {
    Input
  } from './Input';
  import {
    <PERSON>, CardContent, CardHeader, CardTitle
  } from './card';
  interface FormField {
    key: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  options?: {
    value: string;
  label: string;
  }[];
  placeholder?: string;
  } interface CRUDModalProps {
    isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  title: string;
  fields: FormField[];
  initialData?: any;
  mode: 'create' | 'edit';
  loading?: boolean;
  }

const CRUDModal: React.FC<CRUDModalProps> = ({
    isOpen, onClose, onSubmit, title, fields, initialData = {
  }, mode, loading = false
  }) => {
    const [formData, setFormData] = useState<any>({
  });
  const [errors, setErrors] = useState<any>({
  });
  const [submitting, setSubmitting] =
  useState(false);
  useEffect(() => {
    if (isOpen) {
    setFormData(initialData);
  setErrors({
  });
  }
  }, [isOpen, initialData]);
  const handleInputChange = (key: string, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value
  }));
  if (errors[key]) {
    setErrors(prev => ({ ...prev, [key]: null
  }));
  }
  };
  const validateForm = () => {
    const newErrors: any = {
  };
  fields.forEach(field => {
    if (field.required && (!formData[field.key] || formData[field.key].toString().trim() === '')) {
    newErrors[field.key] = `${
    field.label
  } is required`;
  }
  if (field.type === 'email' && formData[field.key]) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(formData[field.key])) {
    newErrors[field.key] = 'Please enter a valid email address';
  }
  }
  });
  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
  if (!validateForm()) {
    return;
  } setSubmitting(true);
  try {
    await onSubmit(formData);
  onClose();
  } catch (error: any) {
    console.error('Form submission error:', error); // Handle API errors
  if (error.message) {
    try {
    const apiErrors = JSON.parse(error.message);
  setErrors(apiErrors);
  } catch {
    setErrors({
    general: error.message
  });
  }
  }
  } finally {
    setSubmitting(false);
  }
  };
  const renderField = (field: FormField) => {
    const value = formData[field.key] || '';
  const error = errors[field.key];
  switch (field.type) {
    case 'select':
  return ( <div key={
    field.key
  } className="space-y-2"> <label className="text-sm font-medium macos-text-primary"> {
    field.label
  } {
    field.required && <span className="text-red-500">*</span>
  } </label> <select value={
    value
  } onChange={(e) => handleInputChange(field.key, e.target.value)
  } className="macos-input w-full" required={
    field.required
  } > <option value="">Select {
    field.label
  }</option> {
    field.options?.map(option => ( <option key={
    option.value
  } value={
    option.value
  }> {
    option.label
  } </option> ))
  } </select> {
    error && <p className="text-sm text-rose-700 dark:text-rose-400">{
    error
  }</p>
  } </div> );
  case 'textarea':
  return ( <div key={
    field.key
  } className="space-y-2"> <label className="text-sm font-medium macos-text-primary"> {
    field.label
  } {
    field.required && <span className="text-red-500">*</span>
  } </label> <textarea value={
    value
  } onChange={(e) => handleInputChange(field.key, e.target.value)
  } placeholder={
    field.placeholder
  } className="macos-input w-full min-h-[100px] resize-y" required={
    field.required
  } /> {
    error && <p className="text-sm text-rose-700 dark:text-rose-400">{
    error
  }</p>
  } </div> );
  default:
  return ( <div key={
    field.key
  } className="space-y-2"> <label className="text-sm font-medium macos-text-primary"> {
    field.label
  } {
    field.required && <span className="text-red-500">*</span>
  } </label>
    <Input type={
    field.type
  } value={
    value
  } onChange={(e) => handleInputChange(field.key, e.target.value)
  } placeholder={
    field.placeholder
  } className="w-full" required={
    field.required
  } /> {
    error && <p className="text-sm text-rose-700 dark:text-rose-400">{
    error
  }</p>
  } </div> );
  }
  };
  if (!isOpen) return null;
  return ( <div className="fixed inset-0 z-50 flex items-center justify-center"> {/* Backdrop */
  } <div className="absolute inset-0 bg-background/50 backdrop-blur-sm" onClick={
    onClose
  } /> {/* Modal */
  }
    <Card className="relative w-full max-w-2xl max-h-[90vh] overflow-hidden macos-card">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
    <CardTitle className="macos-text-primary">{
    title
  }
    </CardTitle>
    <Button variant="ghost" size="sm" onClick={
    onClose
  } className="h-8 w-8 p-0" >
    <X className="h-4 w-4" />
    </Button>
    </CardHeader>
    <CardContent className="overflow-y-auto max-h-[calc(90vh-120px)]"> <form onSubmit={
    handleSubmit
  } className="space-y-6"> {
    errors.general && ( <div className="p-3 bg-red-50 border rounded-lg"> <p className="text-sm text-rose-700 dark:text-rose-400">{
    errors.general
  }</p> </div> )
  } <div className="grid grid-cols-1 md:grid-cols-2 gap-6"> {
    fields.map(renderField)
  } </div> <div className="flex justify-end space-x-3 pt-6 border-t border-border">
    <Button type="button" variant="ghost" onClick={
    onClose
  } disabled={
    submitting
  } > Cancel
    </Button>
    <Button type="submit" disabled={
    submitting
  } className="macos-button" > {
    submitting ? ( <>
    <Loader2 className="w-4 h-4 mr-2 animate-spin" /> {
    mode === 'create' ? 'Creating...' : 'Updating...'
  } </> ) : ( <>
    <Save className="w-4 h-4 mr-2" /> {
    mode === 'create' ? 'Create' : 'Update'
  } </> )
  }
    </Button> </div> </form>
    </CardContent>
    </Card> </div> );
  };
  export default CRUDModal;
