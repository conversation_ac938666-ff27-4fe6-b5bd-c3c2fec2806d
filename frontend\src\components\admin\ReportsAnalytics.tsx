import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    Card, CardContent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/Button';
  import {
    Badge
  } from '../ui/badge';
  import {
    BarChart3, TrendingUp, Users, Calendar, DollarSign, Download, Filter, Eye, FileText, PieChart, Activity, Clock
  } from 'lucide-react';
  const ReportsAnalytics: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const [selectedPeriod, setSelectedPeriod] =
  useState('month');
  const reportCategories = [ {
    id: 'patient-reports', title: 'Patient Reports', description: 'Patient demographics, admissions, and discharge reports', icon: Users, color: 'text-sky-700 dark:text-sky-400', bgColor: 'bg-blue-50', reports: [ {
    name: 'Patient Demographics', lastGenerated: '2024-12-10', status: 'ready'
  }, {
    name: 'Admission Report', lastGenerated: '2024-12-09', status: 'ready'
  }, {
    name: 'Discharge Summary', lastGenerated: '2024-12-08', status: 'generating'
  } ]
  }, {
    id: 'financial-reports', title: 'Financial Reports', description: 'Revenue, billing, and financial analytics', icon: DollarSign, color: 'text-emerald-700 dark:text-emerald-400', bgColor: 'bg-green-50', reports: [ {
    name: 'Revenue Report', lastGenerated: '2024-12-10', status: 'ready'
  }, {
    name: 'Billing Summary', lastGenerated: '2024-12-09', status: 'ready'
  }, {
    name: 'Insurance Claims', lastGenerated: '2024-12-08', status: 'ready'
  } ]
  }, {
    id: 'operational-reports', title: 'Operational Reports', description: 'Staff performance, appointments, and operational metrics', icon: Activity, color: 'text-purple-600', bgColor: 'bg-purple-50', reports: [ {
    name: 'Staff Performance', lastGenerated: '2024-12-10', status: 'ready'
  }, {
    name: 'Appointment Analytics', lastGenerated: '2024-12-09', status: 'ready'
  }, {
    name: 'Department Efficiency', lastGenerated: '2024-12-08', status: 'generating'
  } ]
  }, {
    id: 'clinical-reports', title: 'Clinical Reports', description: 'Medical outcomes, treatment effectiveness, and clinical metrics', icon: FileText, color: 'text-orange-600', bgColor: 'bg-orange-50', reports: [ {
    name: 'Treatment Outcomes', lastGenerated: '2024-12-10', status: 'ready'
  }, {
    name: 'Medication Usage', lastGenerated: '2024-12-09', status: 'ready'
  }, {
    name: 'Lab Test Analytics', lastGenerated: '2024-12-08', status: 'ready'
  } ]
  } ];
  const keyMetrics = [ {
    title: 'Total Patients', value: '1,247', change: '+12%', trend: 'up', icon: Users, color: 'text-sky-700 dark:text-sky-400'
  }, {
    title: 'Monthly Revenue', value: '$284,500', change: '+8%', trend: 'up', icon: DollarSign, color: 'text-emerald-700 dark:text-emerald-400'
  }, {
    title: 'Appointments', value: '2,156', change: '+15%', trend: 'up', icon: Calendar, color: 'text-purple-600'
  }, {
    title: 'Avg Wait Time', value: '18 min', change: '-5%', trend: 'down', icon: Clock, color: 'text-orange-600'
  } ];
  const recentReports = [ {
    id: 1, name: 'Monthly Financial Summary', type: 'Financial', generatedBy: 'System', date: '2024-12-10', size: '2.4 MB', downloads: 15, status: 'ready'
  }, {
    id: 2, name: 'Patient Satisfaction Survey', type: 'Clinical', generatedBy: 'Dr. Johnson', date: '2024-12-09', size: '1.8 MB', downloads: 8, status: 'ready'
  }, {
    id: 3, name: 'Staff Performance Q4', type: 'Operational', generatedBy: 'HR Department', date: '2024-12-08', size: '3.2 MB', downloads: 22, status: 'ready'
  }, {
    id: 4, name: 'Inventory Usage Report', type: 'Operational', generatedBy: 'System', date: '2024-12-07', size: '1.5 MB', downloads: 5, status: 'generating'
  } ];
  const getStatusColor = (status: string) => {
    switch (status) {
    case 'ready': return 'status-success';
  case 'generating': return 'status-warning';
  case 'error': return 'status-error';
  default: return 'bg-muted text-foreground';
  }
  };
  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? (
    <TrendingUp className="w-4 h-4 text-emerald-700 dark:text-emerald-400" /> ) : (
    <TrendingUp className="w-4 h-4 text-rose-700 dark:text-rose-400 transform rotate-180" /> );
  };
  return ( <div className="space-y-6 p-6"> {/* Header */
  } <div className="flex items-center justify-between"> <div> <h1 className="text-3xl font-bold text-foreground">{
    t('reports.title')
  }</h1> <p className="text-muted-foreground mt-2">{
    t('reports.description')
  }</p> </div> <div className="flex space-x-2">
    <Button variant="outline">
    <Filter className="w-4 h-4 mr-2" /> {
    t('reports.filter')
  }
    </Button>
    <Button>
    <FileText className="w-4 h-4 mr-2" /> {
    t('reports.generateReport')
  }
    </Button> </div> </div> {/* Key Metrics */
  } <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"> {
    keyMetrics.map((metric, index) => {
    const IconComponent = metric.icon;
  return (
    <Card key={
    index
  }>
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium text-muted-foreground">{
    metric.title
  }</p> <p className="text-2xl font-bold text-foreground">{
    metric.value
  }</p> <div className="flex items-center mt-1"> {
    getTrendIcon(metric.trend)
  } <span className={`text-sm ml-1 ${
    metric.trend === 'up' ? 'text-emerald-700 dark:text-emerald-400' : 'text-rose-700 dark:text-rose-400'
  }`
  }> {
    metric.change
  } </span> </div> </div>
    <IconComponent className={`w-8 h-8 ${
    metric.color
  }`
  } /> </div>
    </CardContent>
    </Card> );
  })
  } </div> {/* Period Selector */
  } <div className="flex items-center space-x-4"> <span className="text-sm font-medium text-foreground">Time Period:</span> <div className="flex space-x-2"> {['week', 'month', 'quarter', 'year'].map((period) => (
    <Button key={
    period
  } variant={
    selectedPeriod === period ? 'default' : 'outline'
  } size="sm" onClick={() => setSelectedPeriod(period)
  } > {
    period.charAt(0).toUpperCase() + period.slice(1)
  }
    </Button> ))
  } </div> </div> {/* Report Categories */
  } <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {
    reportCategories.map((category) => {
    const IconComponent = category.icon;
  return (
    <Card key={
    category.id
  }>
    <CardHeader>
    <CardTitle className="flex items-center"> <div className={`p-2 rounded-lg ${
    category.bgColor
  } mr-3`
  }>
    <IconComponent className={`w-5 h-5 ${
    category.color
  }`
  } /> </div> {
    category.title
  }
    </CardTitle> <p className="text-sm text-muted-foreground">{
    category.description
  }</p>
    </CardHeader>
    <CardContent> <div className="space-y-3"> {
    category.reports.map((report, index) => ( <div key={
    index
  } className="flex items-center justify-between p-3 bg-muted rounded-lg"> <div> <p className="text-sm font-medium text-foreground">{
    report.name
  }</p> <p className="text-xs text-gray-500">Last: {
    report.lastGenerated
  }</p> </div> <div className="flex items-center space-x-2">
    <Badge className={
    getStatusColor(report.status)
  }> {
    report.status
  }
    </Badge> {
    report.status === 'ready' && (
    <Button size="sm" variant="outline">
    <Download className="w-3 h-3" />
    </Button> )
  } </div> </div> ))
  } </div>
    </CardContent>
    </Card> );
  })
  } </div> {/* Recent Reports */
  }
    <Card>
    <CardHeader>
    <CardTitle className="flex items-center">
    <FileText className="w-5 h-5 mr-2 text-sky-700 dark:text-sky-400" /> Recent Reports
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="space-y-4"> {
    recentReports.map((report) => ( <div key={
    report.id
  } className="flex items-center justify-between p-4 bg-muted rounded-lg"> <div className="flex-1"> <div className="flex items-center justify-between"> <h4 className="text-sm font-medium text-foreground">{
    report.name
  }</h4>
    <Badge className={
    getStatusColor(report.status)
  }> {
    report.status
  }
    </Badge> </div> <div className="mt-1 text-xs text-gray-500"> <span>{
    report.type
  }</span> • <span> Generated by {
    report.generatedBy
  }</span> • <span> {
    report.date
  }</span> • <span> {
    report.size
  }</span> • <span> {
    report.downloads
  } downloads</span> </div> </div> <div className="flex space-x-2 ml-4">
    <Button size="sm" variant="outline">
    <Eye className="w-3 h-3 mr-1" /> View
    </Button> {
    report.status === 'ready' && (
    <Button size="sm">
    <Download className="w-3 h-3 mr-1" /> Download
    </Button> )
  } </div> </div> ))
  } </div>
    </CardContent>
    </Card> {/* Quick Analytics */
  } <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <Card>
    <CardHeader>
    <CardTitle className="flex items-center">
    <BarChart3 className="w-5 h-5 mr-2 text-purple-600" /> Patient Flow Analytics
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="space-y-4"> <div className="flex justify-between items-center"> <span className="text-sm text-muted-foreground">Emergency Admissions</span> <span className="text-sm font-medium">156 this month</span> </div> <div className="flex justify-between items-center"> <span className="text-sm text-muted-foreground">Scheduled Admissions</span> <span className="text-sm font-medium">342 this month</span> </div> <div className="flex justify-between items-center"> <span className="text-sm text-muted-foreground">Average Stay</span> <span className="text-sm font-medium">3.2 days</span> </div> <div className="flex justify-between items-center"> <span className="text-sm text-muted-foreground">Readmission Rate</span> <span className="text-sm font-medium">8.5%</span> </div> </div>
    </CardContent>
    </Card>
    <Card>
    <CardHeader>
    <CardTitle className="flex items-center">
    <PieChart className="w-5 h-5 mr-2 text-emerald-700 dark:text-emerald-400" /> Department Performance
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="space-y-4"> <div className="flex justify-between items-center"> <span className="text-sm text-muted-foreground">Cardiology</span> <span className="text-sm font-medium">95% satisfaction</span> </div> <div className="flex justify-between items-center"> <span className="text-sm text-muted-foreground">Emergency</span> <span className="text-sm font-medium">88% satisfaction</span> </div> <div className="flex justify-between items-center"> <span className="text-sm text-muted-foreground">Orthopedics</span> <span className="text-sm font-medium">92% satisfaction</span> </div> <div className="flex justify-between items-center"> <span className="text-sm text-muted-foreground">Pediatrics</span> <span className="text-sm font-medium">97% satisfaction</span> </div> </div>
    </CardContent>
    </Card> </div> </div> );
  };
  export default ReportsAnalytics;
