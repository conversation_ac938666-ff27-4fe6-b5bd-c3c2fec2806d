/** * FormField Component * Reusable form field component with validation, icons, and consistent styling */ import React, {
    forwardRef
  } from 'react';
  import {
    Eye, EyeOff, AlertCircle, Check
  } from 'lucide-react';
  import {
    Input
  } from '../../../components/ui/Input';
  import {
    Label
  } from '../../../components/ui/label';
  import {
    Textarea
  } from '../../../components/ui/textarea';
  import {
    Select, SelectContent, SelectItem, SelectTrigger, SelectValue
  } from '../../../components/ui/select';
  import {
    Checkbox
  } from '../../../components/ui/checkbox';
  import {
    RadioGroup, RadioGroupItem
  } from '../../../components/ui/radio-group';
  import {
    cn
  } from '../../../lib/utils';
  import type {
    FormFieldConfig, ValidationRule
  } from '../../types/common';
  interface FormFieldProps extends Omit
    <FormFieldConfig, 'name'> {
    name: string;
  value?: any;
  onChange?: (value: any) => void;
  onBlur?: () => void;
  error?: string;
  touched?: boolean;
  className?: string;
  variant?: 'default' | 'glass' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  showValidation?: boolean;
  autoComplete?: string; 'data-testid'?: string;
  }

const FormField = forwardRef
    <HTMLInputElement, FormFieldProps>(({
    name, label, type = 'text', value = '', onChange, onBlur, required = false, placeholder, options = [], validation = [], disabled = false, hidden = false, icon: Icon, description, error, touched = false, className, variant = 'glass', size = 'md', showValidation = true, autoComplete, 'data-testid': testId, ...props
  }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);
  const [isValid, setIsValid] = React.useState<boolean | null>(null); // Validate field value

const validateField = React.useCallback((fieldValue: any) => {
    if (!showValidation) return null;
  for (const rule of validation) {
    switch (rule.type) {
    case 'required':
  if (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === '')) {
    return rule.message;
  } break;
  case 'email':
  if (fieldValue && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(fieldValue)) {
    return rule.message;
  } break;
  case 'min':
  if (fieldValue && fieldValue.length < rule.value) {
    return rule.message;
  } break;
  case 'max':
  if (fieldValue && fieldValue.length > rule.value) {
    return rule.message;
  } break;
  case 'pattern':
  if (fieldValue && !new RegExp(rule.value).test(fieldValue)) {
    return rule.message;
  } break;
  case 'custom':
  if (rule.validator && !rule.validator(fieldValue)) {
    return rule.message;
  } break;
  }
  } return null;
  }, [validation, showValidation]); // Update validation state React.useEffect(() => {
    if (showValidation && touched) {
    const validationError = validateField(value);
  setIsValid(validationError === null);
  }
  }, [value, touched, validateField, showValidation]); // Handle value change

const handleChange = (newValue: any) => {
    onChange?.(newValue);
  }; // Get size classes

const getSizeClasses = () => {
    switch (size) {
    case 'sm': return {
    label: 'text-sm', input: 'text-sm py-2', icon: 'w-4 h-4', description: 'text-xs', error: 'text-xs',
  };
  case 'lg': return {
    label: 'text-base', input: 'text-base py-3', icon: 'w-5 h-5', description: 'text-sm', error: 'text-sm',
  };
  default: return {
    label: 'text-sm', input: 'text-sm py-2.5', icon: 'w-4 h-4', description: 'text-sm', error: 'text-sm',
  };
  }
  };
  const sizeClasses = getSizeClasses();
  const fieldError = error || (touched ? validateField(value) : null);
  const hasError = Boolean(fieldError);
  const hasSuccess = showValidation && touched && !hasError && value;
  if (hidden) {
    return null;
  }

const renderField = () => {
    const baseInputProps = {
    id: name, name, value, disabled, placeholder, autoComplete, 'data-testid': testId, variant, className: cn( sizeClasses.input, hasError && 'border-red-500 focus:border-red-500 focus:ring-red-500', hasSuccess && 'border-green-500 focus:border-green-500 focus:ring-green-500', Icon && 'pl-10', type === 'password' && 'pr-10' ), onChange: (e: React.ChangeEvent
    <HTMLInputElement | HTMLTextAreaElement>) => handleChange(e.target.value), onBlur, ref: type === 'textarea' ? undefined : ref,
  };
  switch (type) {
    case 'textarea':
  return (
    <Textarea {...baseInputProps
  } rows={4
  } className={
    cn(baseInputProps.className, 'resize-none')
  } /> );
  case 'select':
  return (
    <Select value={
    value
  } onValueChange={
    handleChange
  } disabled={
    disabled
  }>
    <SelectTrigger className={
    cn( sizeClasses.input, hasError && 'border-red-500 focus:border-red-500', hasSuccess && 'border-green-500 focus:border-green-500', Icon && 'pl-10' )
  } data-testid={
    testId
  } >
    <SelectValue placeholder={
    placeholder
  } />
    </SelectTrigger>
    <SelectContent> {
    options.map((option) => (
    <SelectItem key={
    option.value
  } value={
    option.value
  }> {
    option.label
  }
    </SelectItem> ))
  }
    </SelectContent>
    </Select> );
  case 'checkbox':
  return ( <div className="flex items-center space-x-2">
    <Checkbox id={
    name
  } checked={
    value
  } onCheckedChange={
    handleChange
  } disabled={
    disabled
  } data-testid={
    testId
  } />
    <Label htmlFor={
    name
  } className={
    cn(sizeClasses.label, 'font-normal')
  }> {
    label
  }
    </Label> </div> );
  case 'radio':
  return (
    <RadioGroup value={
    value
  } onValueChange={
    handleChange
  } disabled={
    disabled
  }> {
    options.map((option) => ( <div key={
    option.value
  } className="flex items-center space-x-2">
    <RadioGroupItem value={
    option.value
  } id={`${
    name
  }-${
    option.value
  }`
  } />
    <Label htmlFor={`${
    name
  }-${
    option.value
  }`
  } className={
    cn(sizeClasses.label, 'font-normal')
  }> {
    option.label
  }
    </Label> </div> ))
  }
    </RadioGroup> );
  case 'password':
  return ( <div className="relative">
    <Input {...baseInputProps
  } type={
    showPassword ? 'text' : 'password'
  } /> <button type="button" className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-muted-foreground" onClick={() => setShowPassword(!showPassword)
  } tabIndex={-1
  } > {
    showPassword ? (
    <EyeOff className={
    sizeClasses.icon
  } /> ) : (
    <Eye className={
    sizeClasses.icon
  } /> )
  } </button> </div> );
  default: return
    <Input {...baseInputProps
  } type={
    type
  } />;
  }
  };
  return ( <div className={
    cn('space-y-2', className)
  }> {/* Label */
  } {
    label && type !== 'checkbox' && (
    <Label htmlFor={
    name
  } className={
    cn( sizeClasses.label, 'font-medium macos-text-primary', required && "after:content-['*'] after:ml-0.5 after:text-red-500" )
  } > {
    label
  }
    </Label> )
  } {/* Field Container */
  } <div className="relative"> {/* Icon */
  } {
    Icon && type !== 'checkbox' && type !== 'radio' && ( <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
    <Icon className={
    sizeClasses.icon
  } /> </div> )
  } {/* Field */
  } {
    renderField()
  } {/* Validation Icon */
  } {
    showValidation && touched && ( <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"> {
    hasError ? (
    <AlertCircle className={
    cn(sizeClasses.icon, 'text-red-500')
  } /> ) : hasSuccess ? (
    <Check className={
    cn(sizeClasses.icon, 'text-green-500')
  } /> ) : null
  } </div> )
  } </div> {/* Description */
  } {
    description && ( <p className={
    cn(sizeClasses.description, 'macos-text-tertiary')
  }> {
    description
  } </p> )
  } {/* Error Message */
  } {
    fieldError && ( <p className={
    cn(sizeClasses.error, 'text-rose-700 dark:text-rose-400 dark:text-red-400 flex items-center gap-1')
  }>
    <AlertCircle className="w-3 h-3" /> {
    fieldError
  } </p> )
  } </div> );
  });
  FormField.displayName = 'FormField';
  export default FormField;
// Preset field variants export

const GlassFormField = forwardRef
    <HTMLInputElement, FormFieldProps>((props, ref) => (
    <FormField {
...props
  } variant="glass" ref={
    ref
  } /> ));
  export

const MinimalFormField = forwardRef
    <HTMLInputElement, FormFieldProps>((props, ref) => (
    <FormField {
...props
  } variant="minimal" ref={
    ref
  } /> )); // Field size variants export

const SmallFormField = forwardRef
    <HTMLInputElement, FormFieldProps>((props, ref) => (
    <FormField {
...props
  } size="sm" ref={
    ref
  } /> ));
  export

const LargeFormField = forwardRef
    <HTMLInputElement, FormFieldProps>((props, ref) => (
    <FormField {
...props
  } size="lg" ref={
    ref
  } /> ));
  GlassFormField.displayName = 'GlassFormField';
  MinimalFormField.displayName = 'MinimalFormField';
  SmallFormField.displayName = 'SmallFormField';
  LargeFormField.displayName = 'LargeFormField';
