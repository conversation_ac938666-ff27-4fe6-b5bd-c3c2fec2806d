import axios from 'axios';
  import type {
    AxiosInstance, AxiosRequestConfig, AxiosResponse
  } from 'axios';
// API Configuration

const API_BASE_URL = 'http://127.0.0.1:8000/api';
  class ApiService {
    private client: AxiosInstance;
  constructor() {
    this.client = axios.create({
    baseURL: API_BASE_URL, timeout: 10000, headers: { 'Content-Type': 'application/json',
  },
  });
  this.setupInterceptors();
  } private setupInterceptors() { // Request interceptor to add auth token this.client.interceptors.request.use( (config) => {
    const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${
    token
  }`;
  } return config;
  }, (error) => {
    return Promise.reject(error);
  } ); // Response interceptor for error handling this.client.interceptors.response.use( (response) => response, async (error) => {
    if (error.response?.status === 401) { // Token expired, try to refresh

const refreshToken = localStorage.getItem('refreshToken');
  if (refreshToken) {
    try {
    const response = await this.refreshToken(refreshToken);
  localStorage.setItem('token', response.access); // Retry the original request

const originalRequest = error.config;
  originalRequest.headers.Authorization = `Bearer ${
    response.access
  }`;
  return this.client.request(originalRequest);
  } catch (refreshError) { // Refresh failed, clear tokens and redirect to login this.clearTokens();
  window.location.href = '/login';
  return Promise.reject(refreshError);
  }
  } else { // No refresh token, redirect to login this.clearTokens();
  window.location.href = '/login';
  }
  } // Handle other errors

const message = error.response?.data?.error || error.response?.data?.message || error.message || 'An error occurred';
  throw new Error(message);
  } );
  } private async refreshToken(refreshToken: string) {
    const response = await axios.post(`${
    API_BASE_URL
  }/auth/token/refresh/`, {
    refresh: refreshToken,
  });
  return response.data;
  } private clearTokens() {
    localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  } // Generic API methods async get
    <T>(url: string, config?: AxiosRequestConfig): Promise
    <T> {
    const response: AxiosResponse
    <T> = await this.client.get(url, config);
  return response.data;
  } async post
    <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise
    <T> {
    const response: AxiosResponse
    <T> = await this.client.post(url, data, config);
  return response.data;
  } async patch
    <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise
    <T> {
    const response: AxiosResponse
    <T> = await this.client.patch(url, data, config);
  return response.data;
  } async delete
    <T>(url: string, config?: AxiosRequestConfig): Promise
    <T> {
    const response: AxiosResponse
    <T> = await this.client.delete(url, config);
  return response.data;
  } async getPaginated
    <T>(url: string, config?: AxiosRequestConfig): Promise
    <PaginatedResponse<T>> {
    const response: AxiosResponse
    <PaginatedResponse<T>> = await this.client.get(url, config);
  return response.data;
  }
  } // Types export interface PaginatedResponse
    <T = any> {
    count: number;
  next: string | null;
  previous: string | null;
  results: T[];
  } // Create API service instance export

const apiService = new ApiService();
// Patient API export

const patientAPI = {
    getPatients: (page = 1, pageSize = 20) => apiService.getPaginated(`/patients/patients/?page=${
    page
  }&page_size=${
    pageSize
  }`), getPatient: (id: number) => apiService.get(`/patients/patients/${
    id
  }/`), createPatient: (data: any) => apiService.post('/patients/patients/', data), updatePatient: (id: number, data: any) => apiService.patch(`/patients/patients/${
    id
  }/`, data), deletePatient: (id: number) => apiService.delete(`/patients/patients/${
    id
  }/`),
  }; // Auth API export

const authAPI = {
    login: (credentials: {
    username: string;
  password: string
  }) => apiService.post('/auth/login/', credentials), register: (userData: any) => apiService.post('/auth/register/', userData), logout: (refreshToken: string) => apiService.post('/auth/logout/', {
    refresh: refreshToken
  }), refreshToken: (refresh: string) => apiService.post('/auth/token/refresh/', {
    refresh
  }), getProfile: () => apiService.get('/auth/profile/'),
  };
  export default apiService;
