import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    <PERSON>, CardContent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/Button';
  import {
    Badge
  } from '../ui/badge';
  import {
    useTheme
  } from '../../hooks/useTheme';
  import {
    Calendar, Clock, User, MapPin, ChevronLeft, ChevronRight, Plus, Edit, X, Phone, Video, AlertCircle
  } from 'lucide-react';
  const MySchedule: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const {
    isDark
  } =
  useTheme();
  const [currentDate, setCurrentDate] =
  useState(new Date());
  const [viewMode, setViewMode] = useState<'day' | 'week'>('day');
  const schedule = { '2024-12-14': [ {
    id: 1, time: '09:00', duration: 30, patient: '<PERSON>', type: 'Follow-up', location: 'Room 205', status: 'confirmed', notes: 'Diabetes management review', contactInfo: '+****************', appointmentType: 'in-person'
  }, {
    id: 2, time: '09:45', duration: 45, patient: '<PERSON>', type: 'Consultation', location: 'Room 205', status: 'confirmed', notes: 'Post-surgery follow-up', contactInfo: '+****************', appointmentType: 'in-person'
  }, {
    id: 3, time: '11:00', duration: 30, patient: 'Emily Davis', type: 'Telemedicine', location: 'Virtual', status: 'pending', notes: 'Asthma check-up', contactInfo: '+****************', appointmentType: 'virtual'
  }, {
    id: 4, time: '14:00', duration: 60, patient: 'Robert Wilson', type: 'Consultation', location: 'Room 205', status: 'confirmed', notes: 'Pain management consultation', contactInfo: '+****************', appointmentType: 'in-person'
  }, {
    id: 5, time: '15:30', duration: 30, patient: 'Lisa Anderson', type: 'Follow-up', location: 'Room 205', status: 'rescheduled', notes: 'Medication review', contactInfo: '+****************', appointmentType: 'in-person'
  } ], '2024-12-15': [ {
    id: 6, time: '10:00', duration: 45, patient: 'John Smith', type: 'New Patient', location: 'Room 205', status: 'confirmed', notes: 'Initial consultation', contactInfo: '+****************', appointmentType: 'in-person'
  } ]
  };
  const workingHours = {
    start: '08:00', end: '17:00', lunchBreak: {
    start: '12:00', end: '13:00'
  }
  };
  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0];
  };
  const getCurrentDaySchedule = () => {
    const dateKey = formatDate(currentDate);
  return schedule[dateKey] || [];
  };
  const getStatusColor = (status: string) => {
    switch (status) {
    case 'confirmed': return 'status-success ';
  case 'pending': return 'status-warning ';
  case 'rescheduled': return 'status-info ';
  case 'cancelled': return 'status-error ';
  default: return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300';
  }
  };
  const getAppointmentTypeIcon = (type: string) => {
    return type === 'virtual' ?
    <Video className="w-4 h-4" /> :
    <User className="w-4 h-4" />;
  };
  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
  newDate.setDate(currentDate.getDate() + (direction === 'next' ? 1 : -1));
  setCurrentDate(newDate);
  };
  const getTodayStats = () => {
    const todaySchedule = getCurrentDaySchedule();
  return {
    total: todaySchedule.length, confirmed: todaySchedule.filter(apt => apt.status === 'confirmed').length, pending: todaySchedule.filter(apt => apt.status === 'pending').length, virtual: todaySchedule.filter(apt => apt.appointmentType === 'virtual').length
  };
  };
  const stats = getTodayStats();
  const renderTimeSlot = (hour: number) => {
    const timeString = `${
    hour.toString().padStart(2, '0')
  }:00`;
  const appointment = getCurrentDaySchedule().find(apt => apt.time === timeString);
  return ( <div key={
    hour
  } className="border-b border-gray-100 min-h-[80px] p-2"> <div className="text-xs text-gray-500 mb-1">{
    timeString
  }</div> {
    appointment ? (
    <Card className="h-full">
    <CardContent className="p-3"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center space-x-2 mb-1"> {
    getAppointmentTypeIcon(appointment.appointmentType)
  } <span className="font-medium text-sm">{
    appointment.patient
  }</span>
    <Badge className={
    getStatusColor(appointment.status)
  } size="sm"> {
    appointment.status
  }
    </Badge> </div> <p className="text-xs text-muted-foreground mb-1">{
    appointment.type
  }</p> <div className="flex items-center space-x-2 text-xs text-gray-500">
    <Clock className="w-3 h-3" /> <span>{
    appointment.duration
  } min</span>
    <MapPin className="w-3 h-3" /> <span>{
    appointment.location
  }</span> </div> </div> <div className="flex space-x-1">
    <Button variant="outline" size="sm">
    <Edit className="w-3 h-3" />
    </Button>
    <Button variant="outline" size="sm">
    <Phone className="w-3 h-3" />
    </Button> </div> </div>
    </CardContent>
    </Card> ) : ( <div className="h-full flex items-center justify-center text-gray-300">
    <Button variant="outline" size="sm" className="opacity-50">
    <Plus className="w-3 h-3 mr-1" /> Add
    </Button> </div> )
  } </div> );
  };
  const renderAppointmentCard = (appointment: any) => (
    <Card key={
    appointment.id
  } className="mb-3">
    <CardContent className="p-4"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center space-x-2 mb-2"> {
    getAppointmentTypeIcon(appointment.appointmentType)
  } <h3 className="font-semibold text-foreground">{
    appointment.patient
  }</h3>
    <Badge className={
    getStatusColor(appointment.status)
  }> {
    appointment.status
  }
    </Badge> </div> <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground mb-2"> <div className="flex items-center space-x-1">
    <Clock className="w-4 h-4" /> <span>{
    appointment.time
  } ({
    appointment.duration
  } min)</span> </div> <div className="flex items-center space-x-1">
    <MapPin className="w-4 h-4" /> <span>{
    appointment.location
  }</span> </div> </div> <p className="text-sm text-foreground mb-2">{
    appointment.type
  }</p> {
    appointment.notes && ( <div className="bg-blue-50 border rounded p-2 mb-2"> <p className="text-xs text-sky-700 dark:text-sky-400">{
    appointment.notes
  }</p> </div> )
  } <p className="text-xs text-gray-500">{
    appointment.contactInfo
  }</p> </div> <div className="flex flex-col space-y-1 ml-4"> {
    appointment.appointmentType === 'virtual' && (
    <Button size="sm">
    <Video className="w-4 h-4 mr-1" /> Join Call
    </Button> )
  }
    <Button variant="outline" size="sm">
    <Edit className="w-4 h-4 mr-1" /> Edit
    </Button>
    <Button variant="outline" size="sm">
    <Phone className="w-4 h-4 mr-1" /> Call
    </Button> {
    appointment.status === 'pending' && (
    <Button variant="outline" size="sm">
    <X className="w-4 h-4 mr-1" /> Cancel
    </Button> )
  } </div> </div>
    </CardContent>
    </Card> );
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  }
    <Card className="glass border-0 shadow-xl">
    <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
    <Calendar className="w-6 h-6 text-white" /> </div> <div>
    <CardTitle className="text-2xl font-bold macos-text-primary">My Schedule
    </CardTitle> <p className="macos-text-secondary">Manage your daily appointments and schedule</p> </div> </div> <div className="flex space-x-2">
    <Button variant="glass" onClick={() => setViewMode(viewMode === 'day' ? 'week' : 'day')
  }> {
    viewMode === 'day' ? 'Week View' : 'Day View'
  }
    </Button>
    <Button variant="glass" className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Add Appointment
    </Button> </div> </div>
    </CardHeader>
    </Card> {/* Statistics */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Today's Appointments</p> <p className="text-2xl font-bold macos-text-primary">{
    stats.total
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
    <Calendar className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Confirmed</p> <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400 dark:text-green-400">{
    stats.confirmed
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
    <User className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Pending</p> <p className="text-2xl font-bold text-amber-700 dark:text-amber-400 dark:text-yellow-400">{
    stats.pending
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
    <AlertCircle className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Virtual Appointments</p> <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">{
    stats.virtual
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
    <Video className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card> </div> {/* Date Navigation */
  }
    <Card className="glass border-0 shadow-lg">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div className="flex items-center space-x-4">
    <Button variant="glass" onClick={() => navigateDate('prev')
  }>
    <ChevronLeft className="w-4 h-4" />
    </Button> <h2 className="text-xl font-semibold macos-text-primary"> {
    currentDate.toLocaleDateString('en-US', {
    weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'
  })
  } </h2>
    <Button variant="glass" onClick={() => navigateDate('next')
  }>
    <ChevronRight className="w-4 h-4" />
    </Button> </div>
    <Button variant="glass" onClick={() => setCurrentDate(new Date())
  }> Today
    </Button> </div>
    </CardContent>
    </Card> {/* Schedule Content */
  } {
    viewMode === 'day' ? ( <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {/* Time Grid */
  }
    <Card className="glass border-0 shadow-lg">
    <CardHeader>
    <CardTitle className="macos-text-primary">Time Grid
    </CardTitle>
    </CardHeader>
    <CardContent className="p-0"> <div className="max-h-96 overflow-y-auto"> {
    Array.from({
    length: 10
  }, (_, i) => i + 8).map(renderTimeSlot)
  } </div>
    </CardContent>
    </Card> {/* Appointments List */
  }
    <Card className="glass border-0 shadow-lg">
    <CardHeader>
    <CardTitle className="macos-text-primary">Appointments
    </CardTitle>
    </CardHeader>
    <CardContent> {
    getCurrentDaySchedule().length > 0 ? ( <div className="space-y-3"> {
    getCurrentDaySchedule().map(renderAppointmentCard)
  } </div> ) : ( <div className="text-center py-8"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
    <Calendar className="w-8 h-8 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary mb-2">No appointments today</h3> <p className="macos-text-secondary mb-4">You have a free day!</p>
    <Button variant="glass" className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> Add Appointment
    </Button> </div> )
  }
    </CardContent>
    </Card> </div> ) : (
    <Card className="glass border-0 shadow-lg">
    <CardHeader>
    <CardTitle className="macos-text-primary">Week View
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="text-center py-8"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
    <Calendar className="w-8 h-8 text-white" /> </div> <p className="macos-text-secondary">Week view coming soon...</p> </div>
    </CardContent>
    </Card> )
  } </div> </div> );
  };
  export default MySchedule;
