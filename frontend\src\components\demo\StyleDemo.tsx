import React from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/button';
  import ThemeToggle from '../ui/ThemeToggle';
  import {
    useTheme
  } from '../../hooks/useTheme';
  import {
    Palette, Sun, Moon, Monitor, Sparkles, Eye, Settings, Heart, Star, Zap
  } from 'lucide-react';
  const StyleDemo: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const {
    mode, effectiveTheme, glassmorphismLevel
  } =
  useTheme();
  return ( <div className="space-y-8 p-6"> {/* Header */
  } <div className="text-center space-y-4"> <h1 className="text-4xl font-bold text-foreground"> 🎨 HMS Style System Demo </h1> <p className="text-lg text-muted-foreground max-w-2xl mx-auto"> Showcase of the unified macOS-style design system with glassmorphism effects and dark mode support. </p> <div className="flex justify-center">
    <ThemeToggle variant="dropdown" /> </div> </div> {/* Current Theme Info */
  }
    <Card className="macos-card max-w-md mx-auto">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Palette className="w-5 h-5" /> Current Theme
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="space-y-2 text-sm"> <div className="flex justify-between"> <span>Mode:</span> <span className="capitalize font-medium">{
    mode
  }</span> </div> <div className="flex justify-between"> <span>Effective:</span> <span className="capitalize font-medium">{
    effectiveTheme
  }</span> </div> <div className="flex justify-between"> <span>Glassmorphism:</span> <span className="capitalize font-medium">{
    glassmorphismLevel
  }</span> </div> </div>
    </CardContent>
    </Card> {/* Button Variants */
  }
    <Card className="macos-card">
    <CardHeader>
    <CardTitle>Button Variants
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
    <Button variant="default">
    <Heart className="w-4 h-4 mr-2" /> Default
    </Button>
    <Button variant="secondary">
    <Star className="w-4 h-4 mr-2" /> Secondary
    </Button>
    <Button variant="outline">
    <Eye className="w-4 h-4 mr-2" /> Outline
    </Button>
    <Button variant="destructive">
    <Zap className="w-4 h-4 mr-2" /> Destructive
    </Button> </div>
    </CardContent>
    </Card> {/* Card Variants */
  } <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Sun className="w-5 h-5 text-yellow-500" /> Light Theme
    </CardTitle>
    </CardHeader>
    <CardContent> <p className="text-muted-foreground"> Beautiful light theme with subtle shadows and clean typography. </p>
    </CardContent>
    </Card>
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Moon className="w-5 h-5 text-blue-400" /> Dark Theme
    </CardTitle>
    </CardHeader>
    <CardContent> <p className="text-muted-foreground"> Elegant dark theme with proper contrast and eye-friendly colors. </p>
    </CardContent>
    </Card>
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Sparkles className="w-5 h-5 text-purple-500" /> Glassmorphism
    </CardTitle>
    </CardHeader>
    <CardContent> <p className="text-muted-foreground"> Modern glassmorphism effects with backdrop blur and transparency. </p>
    </CardContent>
    </Card> </div> {/* Color Palette */
  }
    <Card className="macos-card">
    <CardHeader>
    <CardTitle>Color Palette
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="grid grid-cols-2 md:grid-cols-4 gap-4"> <div className="space-y-2"> <div className="w-full h-12 bg-primary rounded-lg"></div> <p className="text-sm font-medium">Primary</p> </div> <div className="space-y-2"> <div className="w-full h-12 bg-secondary rounded-lg"></div> <p className="text-sm font-medium">Secondary</p> </div> <div className="space-y-2"> <div className="w-full h-12 bg-accent rounded-lg"></div> <p className="text-sm font-medium">Accent</p> </div> <div className="space-y-2"> <div className="w-full h-12 bg-muted rounded-lg"></div> <p className="text-sm font-medium">Muted</p> </div> </div>
    </CardContent>
    </Card> {/* Typography */
  }
    <Card className="macos-card">
    <CardHeader>
    <CardTitle>Typography
    </CardTitle>
    </CardHeader>
    <CardContent className="space-y-4"> <div> <h1 className="text-4xl font-bold text-foreground">Heading 1</h1> <h2 className="text-3xl font-semibold text-foreground">Heading 2</h2> <h3 className="text-2xl font-medium text-foreground">Heading 3</h3> <p className="text-base text-foreground">Regular paragraph text</p> <p className="text-sm text-muted-foreground">Muted text for secondary information</p> </div>
    </CardContent>
    </Card> {/* Status */
  }
    <Card className="macos-card">
    <CardHeader>
    <CardTitle className="flex items-center gap-2">
    <Settings className="w-5 h-5" /> System Status
    </CardTitle>
    </CardHeader>
    <CardContent> <div className="space-y-3"> <div className="flex items-center justify-between"> <span>✅ Theme System</span> <span className="text-emerald-700 dark:text-emerald-400 font-medium">Working</span> </div> <div className="flex items-center justify-between"> <span>✅ Dark Mode</span> <span className="text-emerald-700 dark:text-emerald-400 font-medium">Working</span> </div> <div className="flex items-center justify-between"> <span>✅ Glassmorphism</span> <span className="text-emerald-700 dark:text-emerald-400 font-medium">Working</span> </div> <div className="flex items-center justify-between"> <span>✅ Responsive Design</span> <span className="text-emerald-700 dark:text-emerald-400 font-medium">Working</span> </div> <div className="flex items-center justify-between"> <span>✅ Accessibility</span> <span className="text-emerald-700 dark:text-emerald-400 font-medium">Working</span> </div> </div>
    </CardContent>
    </Card> </div> );
  };
  export default StyleDemo;
