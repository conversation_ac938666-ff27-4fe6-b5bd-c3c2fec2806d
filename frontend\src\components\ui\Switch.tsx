import React from 'react';
  import {
    cn
  } from '../../lib/utils';
  interface SwitchProps {
    checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'glass';
  label?: string;
  description?: string;
  className?: string;
  }

const Switch: React.FC
    <SwitchProps> = ({
    checked, onChange, disabled = false, size = 'md', variant = 'default', label, description, className,
  }) => {
    const sizeClasses = {
    sm: {
    track: 'w-8 h-4', thumb: 'w-3 h-3', translate: 'translate-x-4',
  }, md: {
    track: 'w-11 h-6', thumb: 'w-5 h-5', translate: 'translate-x-5',
  }, lg: {
    track: 'w-14 h-7', thumb: 'w-6 h-6', translate: 'translate-x-7',
  },
  };
  const variantClasses = {
    default: {
    track: checked ? 'bg-primary' : 'bg-secondary border border-border', thumb: 'bg-background shadow-sm',
  }, glass: {
    track: checked ? 'bg-primary/20 border border-primary/30 backdrop-blur-sm' : 'bg-secondary/50 border border-border backdrop-blur-sm', thumb: 'bg-background/90 shadow-lg backdrop-blur-sm',
  },
  };
  const handleToggle = () => {
    if (!disabled) {
    onChange(!checked);
  }
  };
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === ' ' || event.key === 'Enter') {
    event.preventDefault();
  handleToggle();
  }
  };
  return ( <div className={
    cn('flex items-start gap-3', className)
  }> {/* Switch */
  } <button type="button" role="switch" aria-checked={
    checked
  } onClick={
    handleToggle
  } onKeyDown={
    handleKeyDown
  } disabled={
    disabled
  } className={
    cn( 'relative inline-flex items-center rounded-full macos-transition macos-focus', 'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2', sizeClasses[size].track, variantClasses[variant].track, disabled && 'opacity-50 cursor-not-allowed' )
  } > {/* Thumb */
  } <span className={
    cn( 'inline-block rounded-full macos-transition transform', sizeClasses[size].thumb, variantClasses[variant].thumb, checked ? sizeClasses[size].translate : 'translate-x-0.5' )
  } /> </button> {/* Label and Description */
  } {(label || description) && ( <div className="flex-1"> {
    label && ( <label className={
    cn( 'text-sm font-medium macos-text-primary cursor-pointer', disabled && 'cursor-not-allowed opacity-50' )
  } onClick={!disabled ? handleToggle : undefined
  } > {
    label
  } </label> )
  } {
    description && ( <p className={
    cn( 'text-xs macos-text-secondary mt-1', disabled && 'opacity-50' )
  }> {
    description
  } </p> )
  } </div> )
  } </div> );
  }; // Switch Group for multiple related switches interface SwitchGroupProps {
    title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  }

const SwitchGroup: React.FC
    <SwitchGroupProps> = ({
    title, description, children, className,
  }) => {
    return ( <div className={
    cn('space-y-4', className)
  }> {(title || description) && ( <div> {
    title && ( <h3 className="text-base font-semibold macos-text-primary"> {
    title
  } </h3> )
  } {
    description && ( <p className="text-sm macos-text-secondary mt-1"> {
    description
  } </p> )
  } </div> )
  } <div className="space-y-3"> {
    children
  } </div> </div> );
  }; // Checkbox component with similar styling interface CheckboxProps {
    checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  indeterminate?: boolean;
  label?: string;
  description?: string;
  variant?: 'default' | 'glass';
  className?: string;
  }

const Checkbox: React.FC
    <CheckboxProps> = ({
    checked, onChange, disabled = false, indeterminate = false, label, description, variant = 'default', className,
  }) => {
    const variantClasses = {
    default: checked || indeterminate ? 'bg-primary border-primary text-primary-foreground' : 'bg-background border-border hover:border-primary', glass: checked || indeterminate ? 'bg-primary/20 border-primary/30 text-primary backdrop-blur-sm' : 'bg-background/50 border-border backdrop-blur-sm hover:border-primary',
  };
  const handleToggle = () => {
    if (!disabled) {
    onChange(!checked);
  }
  };
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === ' ') {
    event.preventDefault();
  handleToggle();
  }
  };
  return ( <div className={
    cn('flex items-start gap-3', className)
  }> {/* Checkbox */
  } <button type="button" role="checkbox" aria-checked={
    indeterminate ? 'mixed' : checked
  } onClick={
    handleToggle
  } onKeyDown={
    handleKeyDown
  } disabled={
    disabled
  } className={
    cn( 'w-4 h-4 rounded border-2 flex items-center justify-center', 'macos-transition macos-focus focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2', variantClasses[variant], disabled && 'opacity-50 cursor-not-allowed' )
  } > {
    checked && !indeterminate && ( <svg className="w-3 h-3" viewBox="0 0 20 20" fill="currentColor"> <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /> </svg> )
  } {
    indeterminate && ( <svg className="w-3 h-3" viewBox="0 0 20 20" fill="currentColor"> <path fillRule="evenodd" d="M4 10a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1z" clipRule="evenodd" /> </svg> )
  } </button> {/* Label and Description */
  } {(label || description) && ( <div className="flex-1"> {
    label && ( <label className={
    cn( 'text-sm font-medium macos-text-primary cursor-pointer', disabled && 'cursor-not-allowed opacity-50' )
  } onClick={!disabled ? handleToggle : undefined
  } > {
    label
  } </label> )
  } {
    description && ( <p className={
    cn( 'text-xs macos-text-secondary mt-1', disabled && 'opacity-50' )
  }> {
    description
  } </p> )
  } </div> )
  } </div> );
  };
  export {
    Switch, SwitchGroup, Checkbox
  };
