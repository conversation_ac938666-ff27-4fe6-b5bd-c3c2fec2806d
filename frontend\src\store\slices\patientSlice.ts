/** * Patient Slice using shared CRUD patterns * Refactored to use the generic createCrudSlice factory */ import {
    createCrudSlice
  } from '../../shared/store/createCrudSlice';
  import type {
    BaseEntity
  } from '../../shared/types/api';
// Define Patient interface extending BaseEntity
interface Patient extends BaseEntity {
  patient_id: string;
  user: {
    id: number;
    full_name: string;
    email: string;
    phone_number?: string;
  };
  blood_group?: string;
  allergies?: string;
  chronic_conditions?: string;
  current_medications?: string;
  insurance_provider?: string;
  insurance_policy_number?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
}

// Mock patient service - in real app, this would be imported from services

const patientService = {
    getAll: async () => ({
    data: []
  }), getById: async (id: string | number) => ({
    data: null
  }), create: async (data: Partial
    <Patient>) => ({
    data: data as Patient
  }), update: async (id: string | number, data: Partial
    <Patient>) => ({
    data: data as Patient
  }), deleteById: async (id: string | number) => {
  }, bulkDelete: async (ids: (string | number)[]) => {
  },
  }; // Create the patient slice using the generic factory

const patientCrud = createCrudSlice
    <Patient>( 'patient', patientService as any, // Additional custom reducers specific to patients {
    updateMedicalInfo: (state, action) => {
    const {
    id, medicalInfo
  } = action.payload;
  const patient = state.items.find(item => item.id === id);
  if (patient) {
    Object.assign(patient, medicalInfo);
  }
  if (state.selectedItem?.id === id) {
    Object.assign(state.selectedItem, medicalInfo);
  }
  }, filterByBloodGroup: (state, action) => {
    state.filters = { ...state.filters, blood_group: action.payload
  };
  }, filterByInsurance: (state, action) => {
    state.filters = { ...state.filters, insurance_provider: action.payload
  };
  },
  } ); // Export the slice components export

const {
    slice, actions, reducer, thunks
  } = patientCrud; // Export specific actions for easier use export

const {
    setSelectedItem: setSelectedPatient, setFilters, updateFilters, clearFilters, setSearchQuery, clearError, reset, optimisticAdd, optimisticUpdate, optimisticRemove, // Custom actions updateMedicalInfo, filterByBloodGroup, filterByInsurance,
  } = actions; // Export thunks for async operations export

const {
    fetchAll: fetchPatients, fetchById: fetchPatient, create: createPatient, update: updatePatient, delete: deletePatient, bulkDelete: bulkDeletePatients,
  } = thunks; // Export the reducer as default export default reducer;
// Legacy exports for backward compatibility export

const setPatients = (patients: Patient[]) => actions.optimisticAdd(patients[0]);
// This is a simplified mapping export

const setLoading = (loading: boolean) => loading ? {
    type: 'patient/fetchAll/pending'
  } : {
    type: 'patient/fetchAll/fulfilled', payload: {
    data: []
  }
  };
  export

const setError = (error: string | null) => ({
    type: 'patient/fetchAll/rejected', payload: error
  });
