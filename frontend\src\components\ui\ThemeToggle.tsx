import React from 'react';
  import {
    useDispatch
  } from 'react-redux';
  import {
    Sun, Moon, Monitor
  } from 'lucide-react';
  import {
    useTheme
  } from '../../hooks/useTheme';
  import {
    setThemeMode, toggleTheme
  } from '../../store/slices/themeSlice';
  import type {
    ThemeMode
  } from '../../store/slices/themeSlice';
  interface ThemeToggleProps {
    variant?: 'button' | 'dropdown' | 'simple';
  className?: string;
  }

const ThemeToggle: React.FC<ThemeToggleProps> = ({
    variant = 'simple', className = ''
  }) => {
    const dispatch =
  useDispatch();
  const {
    mode, effectiveTheme
  } =
  useTheme();
  const handleToggle = () => {
    dispatch(toggleTheme());
  };
  const handleModeSelect = (newMode: ThemeMode) => {
    dispatch(setThemeMode(newMode));
  };
  const getIcon = () => {
    switch (mode) {
    case 'light': return
    <Sun className="h-4 w-4" />;
  case 'dark': return
    <Moon className="h-4 w-4" />;
  case 'system': return
    <Monitor className="h-4 w-4" />;
  default: return
    <Sun className="h-4 w-4" />;
  }
  };
  if (variant === 'simple') {
    return ( <button onClick={
    handleToggle
  } className={` macos-button p-2 rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 ${
    className
  } `
  } title={`Current theme: ${
    mode
  } (${
    effectiveTheme
  })`
  } > {
    getIcon()
  } </button> );
  }
  if (variant === 'dropdown') {
    return ( <div className={`relative ${
    className
  }`
  }> <div className="glass rounded-lg p-1 flex space-x-1"> {(['light', 'dark', 'system'] as ThemeMode[]).map((themeMode) => ( <button key={
    themeMode
  } onClick={() => handleModeSelect(themeMode)
  } className={` p-2 rounded-md transition-all duration-200 ${
    mode === themeMode ? 'bg-primary text-primary-foreground shadow-md' : 'hover:bg-secondary text-muted-foreground hover:text-foreground'
  } `
  } title={`Switch to ${
    themeMode
  } theme`
  } > {
    themeMode === 'light' &&
    <Sun className="h-4 w-4" />
  } {
    themeMode === 'dark' &&
    <Moon className="h-4 w-4" />
  } {
    themeMode === 'system' &&
    <Monitor className="h-4 w-4" />
  } </button> ))
  } </div> </div> );
  }
  if (variant === 'button') {
    return ( <button onClick={
    handleToggle
  } className={` macos-button px-4 py-2 rounded-lg transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 ${
    className
  } `
  } > {
    getIcon()
  } <span className="text-sm font-medium capitalize"> {
    mode
  } {
    mode === 'system' && `(${
    effectiveTheme
  })`
  } </span> </button> );
  } return null;
  };
  export default ThemeToggle;
