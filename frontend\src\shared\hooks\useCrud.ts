/** * Generic CRUD Hook * Provides complete CRUD operations with loading states, error handling, and pagination */ import {
    useState, useCallback, useEffect
  } from 'react';
  import {
    BaseApiService
  } from '../services/BaseApiService';
  import type {
    UseCrudResult, LoadingState, ErrorState, PaginationParams, FilterParams, CrudOperation, BaseEntity, PaginatedResponse
  } from '../types/api';
  interface UseCrudOptions
    <T> {
    immediate?: boolean;
  initialParams?: PaginationParams & FilterParams;
  onSuccess?: (operation: CrudOperation, data: T | T[]) => void;
  onError?: (operation: CrudOperation, error: string) => void;
  optimisticUpdates?: boolean;
  }

const initialLoadingState: LoadingState = {
    list: false, create: false, update: false, delete: false,
  };
  const initialErrorState: ErrorState = {
    list: null, create: null, update: null, delete: null,
  }; /** * Generic CRUD hook for managing entity operations */ export

function useCrud
    <T extends BaseEntity>( service: BaseApiService
    <T>, options: UseCrudOptions
    <T> = {
  } ): UseCrudResult
    <T> {
    const {
    immediate = false, initialParams = {
  }, onSuccess, onError, optimisticUpdates = true,
  } = options; // State management

const [items, setItems] = useState
    <T[]>([]);
  const [selectedItem, setSelectedItem] = useState
    <T | null>(null);
  const [loading, setLoading] = useState
    <LoadingState>(initialLoadingState);
  const [error, setError] = useState
    <ErrorState>(initialErrorState);
  const [pagination, setPagination] = useState
    <PaginatedResponse<T>['pagination'] | null>(null); // Helper

function to update loading state

const setOperationLoading =
  useCallback((operation: CrudOperation, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [operation]: isLoading
  }));
  }, []); // Helper

function to update error state

const setOperationError =
  useCallback((operation: CrudOperation, errorMessage: string | null) => {
    setError(prev => ({ ...prev, [operation]: errorMessage
  }));
  }, []); // Fetch all items

const fetchAll =
  useCallback(async (params?: PaginationParams & FilterParams) => {
    const operation: CrudOperation = 'list';
  setOperationLoading(operation, true);
  setOperationError(operation, null);
  try {
    const response = await service.getAll({ ...initialParams, ...params
  });
  setItems(response.data);
  if ('pagination' in response) {
    setPagination(response.pagination);
  } onSuccess?.(operation, response.data);
  } catch (err: any) {
    const errorMessage = err.message || 'Failed to fetch items';
  setOperationError(operation, errorMessage);
  onError?.(operation, errorMessage);
  } finally {
    setOperationLoading(operation, false);
  }
  }, [service, initialParams, onSuccess, onError, setOperationLoading, setOperationError]); // Fetch single item by ID

const fetchById =
  useCallback(async (id: string | number) => {
    const operation: CrudOperation = 'read';
  setOperationLoading(operation, true);
  setOperationError(operation, null);
  try {
    const response = await service.getById(id);
  setSelectedItem(response.data);
  onSuccess?.(operation, response.data);
  } catch (err: any) {
    const errorMessage = err.message || 'Failed to fetch item';
  setOperationError(operation, errorMessage);
  onError?.(operation, errorMessage);
  } finally {
    setOperationLoading(operation, false);
  }
  }, [service, onSuccess, onError, setOperationLoading, setOperationError]); // Create new item

const create =
  useCallback(async (data: Partial
    <T>): Promise
    <T> => {
    const operation: CrudOperation = 'create';
  setOperationLoading(operation, true);
  setOperationError(operation, null);
  try {
    const response = await service.create(data);
  const newItem = response.data; // Optimistic update
  if (optimisticUpdates) {
    setItems(prev => [newItem, ...prev]);
  } onSuccess?.(operation, newItem);
  return newItem;
  } catch (err: any) {
    const errorMessage = err.message || 'Failed to create item';
  setOperationError(operation, errorMessage);
  onError?.(operation, errorMessage);
  throw err;
  } finally {
    setOperationLoading(operation, false);
  }
  }, [service, optimisticUpdates, onSuccess, onError, setOperationLoading, setOperationError]); // Update existing item

const update =
  useCallback(async (id: string | number, data: Partial
    <T>): Promise
    <T> => {
    const operation: CrudOperation = 'update';
  setOperationLoading(operation, true);
  setOperationError(operation, null); // Store original item for rollback

const originalItem = items.find(item => item.id === id);
  try { // Optimistic update
  if (optimisticUpdates && originalItem) {
    const updatedItem = { ...originalItem, ...data
  };
  setItems(prev => prev.map(item => item.id === id ? updatedItem : item));
  if (selectedItem?.id === id) {
    setSelectedItem(updatedItem);
  }
  }

const response = await service.update(id, data);
  const updatedItem = response.data; // Update with server response setItems(prev => prev.map(item => item.id === id ? updatedItem : item));
  if (selectedItem?.id === id) {
    setSelectedItem(updatedItem);
  } onSuccess?.(operation, updatedItem);
  return updatedItem;
  } catch (err: any) { // Rollback optimistic update
  if (optimisticUpdates && originalItem) {
    setItems(prev => prev.map(item => item.id === id ? originalItem : item));
  if (selectedItem?.id === id) {
    setSelectedItem(originalItem);
  }
  }

const errorMessage = err.message || 'Failed to update item';
  setOperationError(operation, errorMessage);
  onError?.(operation, errorMessage);
  throw err;
  } finally {
    setOperationLoading(operation, false);
  }
  }, [service, items, selectedItem, optimisticUpdates, onSuccess, onError, setOperationLoading, setOperationError]); // Delete item

const deleteItem =
  useCallback(async (id: string | number): Promise<void> => {
    const operation: CrudOperation = 'delete';
  setOperationLoading(operation, true);
  setOperationError(operation, null); // Store original item for rollback

const originalItem = items.find(item => item.id === id);
  try { // Optimistic update
  if (optimisticUpdates) {
    setItems(prev => prev.filter(item => item.id !== id));
  if (selectedItem?.id === id) {
    setSelectedItem(null);
  }
  } await service.deleteById(id); // Ensure item is removed (in case optimistic update was disabled)
  if (!optimisticUpdates) {
    setItems(prev => prev.filter(item => item.id !== id));
  if (selectedItem?.id === id) {
    setSelectedItem(null);
  }
  } onSuccess?.(operation, originalItem || {
  } as T);
  } catch (err: any) { // Rollback optimistic update
  if (optimisticUpdates && originalItem) {
    setItems(prev => [...prev, originalItem].sort((a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime() ));
  }

const errorMessage = err.message || 'Failed to delete item';
  setOperationError(operation, errorMessage);
  onError?.(operation, errorMessage);
  throw err;
  } finally {
    setOperationLoading(operation, false);
  }
  }, [service, items, selectedItem, optimisticUpdates, onSuccess, onError, setOperationLoading, setOperationError]); // Bulk delete items

const bulkDelete =
  useCallback(async (ids: (string | number)[]): Promise<void> => {
    const operation: CrudOperation = 'delete';
  setOperationLoading(operation, true);
  setOperationError(operation, null); // Store original items for rollback

const originalItems = items.filter(item => ids.includes(item.id));
  try { // Optimistic update
  if (optimisticUpdates) {
    setItems(prev => prev.filter(item => !ids.includes(item.id)));
  if (selectedItem && ids.includes(selectedItem.id)) {
    setSelectedItem(null);
  }
  } await service.bulkDelete(ids); // Ensure items are removed (in case optimistic update was disabled)
  if (!optimisticUpdates) {
    setItems(prev => prev.filter(item => !ids.includes(item.id)));
  if (selectedItem && ids.includes(selectedItem.id)) {
    setSelectedItem(null);
  }
  } onSuccess?.(operation, originalItems);
  } catch (err: any) { // Rollback optimistic update
  if (optimisticUpdates) {
    setItems(prev => [...prev, ...originalItems].sort((a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime() ));
  }

const errorMessage = err.message || 'Failed to delete items';
  setOperationError(operation, errorMessage);
  onError?.(operation, errorMessage);
  throw err;
  } finally {
    setOperationLoading(operation, false);
  }
  }, [service, items, selectedItem, optimisticUpdates, onSuccess, onError, setOperationLoading, setOperationError]); // Select item

const selectItem =
  useCallback((item: T | null) => {
    setSelectedItem(item);
  }, []); // Clear error for specific operation

const clearError =
  useCallback((operation?: CrudOperation) => {
    if (operation) {
    setOperationError(operation, null);
  } else {
    setError(initialErrorState);
  }
  }, [setOperationError]); // Reset all state

const reset =
  useCallback(() => {
    setItems([]);
  setSelectedItem(null);
  setLoading(initialLoadingState);
  setError(initialErrorState);
  setPagination(null);
  }, []); // Initial fetch
  useEffect(() => {
    if (immediate) {
    fetchAll(initialParams);
  }
  }, [immediate, fetchAll, initialParams]);
  return {
    items, selectedItem, loading, error, pagination, // Actions fetchAll, fetchById, create, update, delete: deleteItem, bulkDelete, // Utilities selectItem, clearError, reset,
  };
  }
