import React from 'react';
  import {
    cva, type VariantProps
  } from 'class-variance-authority';
  import {
    cn
  } from '../../lib/utils';
  const progressVariants = cva( 'relative overflow-hidden macos-transition', {
    variants: {
    variant: {
    default: 'macos-progress', linear: 'h-1 bg-secondary rounded-full', thick: 'h-3 bg-secondary rounded-full', glass: 'macos-progress',
  }, size: {
    sm: 'h-1', md: 'h-2', lg: 'h-3', xl: 'h-4',
  },
  }, defaultVariants: {
    variant: 'default', size: 'md',
  },
  } );
  interface ProgressProps extends React.HTMLAttributes
    <HTMLDivElement>, VariantProps<typeof progressVariants> {
    value: number;
  max?: number;
  showLabel?: boolean;
  label?: string;
  animated?: boolean;
  color?: 'primary' | 'success' | 'warning' | 'error';
  }

const Progress: React.FC
    <ProgressProps> = ({
    value, max = 100, showLabel = false, label, animated = false, color = 'primary', variant, size, className, ...props
  }) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  const colorClasses = {
    primary: 'macos-progress-bar', success: 'bg-gradient-to-r from-green-500 to-green-400', warning: 'bg-gradient-to-r from-yellow-500 to-yellow-400', error: 'bg-gradient-to-r from-red-500 to-red-400',
  };
  return ( <div className="space-y-2"> {(showLabel || label) && ( <div className="flex justify-between items-center"> <span className="text-sm font-medium macos-text-primary"> {
    label || 'Progress'
  } </span> <span className="text-sm macos-text-secondary"> {
    Math.round(percentage)
  }% </span> </div> )
  } <div className={
    cn(progressVariants({
    variant, size, className
  }))
  } {...props
  } > <div className={
    cn( 'h-full transition-all duration-500 ease-out rounded-full', colorClasses[color], animated && 'animate-pulse' )
  } style={{
    width: `${
    percentage
  }%`
  }
  } /> </div> </div> );
  }; // Circular Progress Component interface CircularProgressProps {
    value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  showLabel?: boolean;
  label?: string;
  color?: 'primary' | 'success' | 'warning' | 'error';
  className?: string;
  }

const CircularProgress: React.FC
    <CircularProgressProps> = ({
    value, max = 100, size = 120, strokeWidth = 8, showLabel = true, label, color = 'primary', className,
  }) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;
  const colorClasses = {
    primary: 'stroke-primary', success: 'stroke-green-500', warning: 'stroke-yellow-500', error: 'stroke-red-500',
  };
  return ( <div className={
    cn('relative inline-flex items-center justify-center', className)
  }> <svg width={
    size
  } height={
    size
  } className="transform -rotate-90" > {/* Background circle */
  } <circle cx={
    size / 2
  } cy={
    size / 2
  } r={
    radius
  } stroke="currentColor" strokeWidth={
    strokeWidth
  } fill="none" className="macos-text-tertiary opacity-20" /> {/* Progress circle */
  } <circle cx={
    size / 2
  } cy={
    size / 2
  } r={
    radius
  } stroke="currentColor" strokeWidth={
    strokeWidth
  } fill="none" strokeDasharray={
    strokeDasharray
  } strokeDashoffset={
    strokeDashoffset
  } strokeLinecap="round" className={
    cn('transition-all duration-500 ease-out', colorClasses[color])
  } /> </svg> {
    showLabel && ( <div className="absolute inset-0 flex flex-col items-center justify-center"> <span className="text-2xl font-bold macos-text-primary"> {
    Math.round(percentage)
  }% </span> {
    label && ( <span className="text-sm macos-text-secondary mt-1"> {
    label
  } </span> )
  } </div> )
  } </div> );
  }; // Step Progress Component interface StepProgressProps {
    steps: Array<{
    label: string;
  completed: boolean;
  current?: boolean;
  }>;
  className?: string;
  }

const StepProgress: React.FC
    <StepProgressProps> = ({
    steps, className
  }) => {
    return ( <div className={
    cn('flex items-center justify-between', className)
  }> {
    steps.map((step, index) => (
    <React.Fragment key={
    index
  }> <div className="flex flex-col items-center"> <div className={
    cn( 'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium macos-transition', step.completed ? 'bg-primary text-primary-foreground' : step.current ? 'bg-primary/20 text-primary border-2 border-primary' : 'bg-secondary macos-text-tertiary' )
  } > {
    step.completed ? '✓' : index + 1
  } </div> <span className={
    cn( 'text-xs mt-2 text-center max-w-20', step.completed || step.current ? 'macos-text-primary font-medium' : 'macos-text-secondary' )
  } > {
    step.label
  } </span> </div> {
    index < steps.length - 1 && ( <div className={
    cn( 'flex-1 h-0.5 mx-4 macos-transition', steps[index + 1]?.completed ? 'bg-primary' : 'bg-border' )
  } /> )
  }
    </React.Fragment> ))
  } </div> );
  };
  export {
    Progress, CircularProgress, StepProgress
  };
