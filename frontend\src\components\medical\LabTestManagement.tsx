import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    Card, CardContent, CardHeader, CardTitle
  } from '../ui/card';
  import {
    Button
  } from '../ui/Button';
  import {
    Badge
  } from '../ui/badge';
  import {
    Input
  } from '../ui/Input';
  import {
    useTheme
  } from '../../hooks/useTheme';
  import {
    getStatusClass, getPriorityClass
  } from '../../utils/styleUtils';
  import CRUDModal from '../ui/CRUDModal';
  import {
    crudService
  } from '../../services/crudService';
  import {
    TestTube, Plus, Search, Clock, Eye, Edit, Download, Filter, AlertCircle, CheckCircle, XCircle
  } from 'lucide-react';
  interface LabTest {
    id: string;
  patientId: string;
  patientName: string;
  doctorName: string;
  testName: string;
  testType: string;
  orderDate: string;
  sampleDate?: string;
  resultDate?: string;
  status: 'ordered' | 'sample-collected' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'routine' | 'urgent' | 'stat';
  results?: string;
  normalRange?: string;
  notes?: string;
  }

const LabTestManagement: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const {
    isDark
  } =
  useTheme();
  const [searchTerm, setSearchTerm] =
  useState('');
  const [selectedFilter, setSelectedFilter] =
  useState('all');
  const [showAddForm, setShowAddForm] =
  useState(false); // Modal state

const [showModal, setShowModal] =
  useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [selectedTest, setSelectedTest] = useState
    <LabTest | null>(null);
  const [loading, setLoading] =
  useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null); // Mock data - replace with API call

const labTests: LabTest[] = [ {
    id: 'LT001', patientId: 'P001', patientName: 'Sarah Johnson', doctorName: 'Dr. Smith', testName: 'Complete Blood Count (CBC)', testType: 'Blood Test', orderDate: '2024-12-10', sampleDate: '2024-12-10', resultDate: '2024-12-11', status: 'completed', priority: 'routine', results: 'WBC: 7.2, RBC: 4.5, Hemoglobin: 13.8', normalRange: 'WBC: 4.0-11.0, RBC: 4.2-5.4, Hgb: 12.0-15.5', notes: 'All values within normal range'
  }, {
    id: 'LT002', patientId: 'P002', patientName: 'Michael Brown', doctorName: 'Dr. Davis', testName: 'HbA1c', testType: 'Blood Test', orderDate: '2024-12-09', sampleDate: '2024-12-09', status: 'in-progress', priority: 'urgent', normalRange: '< 7.0%'
  }, {
    id: 'LT003', patientId: 'P003', patientName: 'Emily Wilson', doctorName: 'Dr. Johnson', testName: 'Chest X-Ray', testType: 'Imaging', orderDate: '2024-12-08', sampleDate: '2024-12-08', resultDate: '2024-12-08', status: 'completed', priority: 'routine', results: 'No acute findings. Lungs clear.', notes: 'Normal chest X-ray'
  }, {
    id: 'LT004', patientId: 'P004', patientName: 'Robert Davis', doctorName: 'Dr. Wilson', testName: 'Lipid Panel', testType: 'Blood Test', orderDate: '2024-12-12', status: 'ordered', priority: 'routine', normalRange: 'Total Cholesterol: < 200 mg/dL'
  } ]; // CRUD Handlers

const handleCreateTest = () => {
    setSelectedTest(null);
  setModalMode('create');
  setShowModal(true);
  };
  const handleViewTest = (test: LabTest) => {
    alert(`Viewing Lab Test\n\nTest: ${
    test.testName
  }\nPatient: ${
    test.patientName
  }\nStatus: ${
    test.status
  }\nOrdered: ${
    test.orderDate
  }\n\nThis will open the detailed test view.`);
  };
  const handleEditTest = (test: LabTest) => {
    setSelectedTest(test);
  setModalMode('edit');
  setShowModal(true);
  };
  const handleSubmitTest = async (testData: any) => {
    try {
    if (modalMode === 'create') {
    await crudService.createLabTest(testData);
  } else
  if (selectedTest) {
    await crudService.updateLabTest(selectedTest.id, testData);
  } // TODO: Refresh tests list alert('Lab test saved successfully!');
  } catch (error: any) {
    console.error('Failed to save lab test:', error);
  throw error;
  }
  };
  const handleDownloadReport = (test: LabTest) => { // Create a downloadable lab report

const reportData = ` LAB TEST REPORT =============== Patient Information: - Name: ${
    test.patientName
  } - Patient ID: ${
    test.patientId
  } - Date of Birth: [Not specified] Test Information: - Test Name: ${
    test.testName
  } - Test Code: ${
    test.testCode
  } - Order Date: ${
    test.orderDate
  } - Collection Date: ${
    test.collectionDate
  } - Result Date: ${
    test.resultDate
  } - Status: ${
    test.status
  } - Priority: ${
    test.priority
  } Results: ${
    test.result
  } Normal Range: ${
    test.normalRange
  } Technician: ${
    test.technician
  } Department: ${
    test.department
  } Comments: ${
    test.comments
  } Generated on: ${
    new Date().toLocaleString()
  } `;
  const blob = new Blob([reportData], {
    type: 'text/plain'
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `lab-report-${
    test.id
  }-${
    test.patientName.replace(/\s+/g, '-')
  }.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  }; // Form fields for lab test modal

const labTestFormFields = [ {
    key: 'patientName', label: 'Patient Name', type: 'text' as const, required: true
  }, {
    key: 'patientId', label: 'Patient ID', type: 'text' as const, required: true
  }, {
    key: 'testName', label: 'Test Name', type: 'text' as const, required: true
  }, {
    key: 'testCode', label: 'Test Code', type: 'text' as const, required: true
  }, {
    key: 'testCategory', label: 'Test Category', type: 'select' as const, required: true, options: [ {
    value: 'hematology', label: 'Hematology'
  }, {
    value: 'chemistry', label: 'Chemistry'
  }, {
    value: 'microbiology', label: 'Microbiology'
  }, {
    value: 'immunology', label: 'Immunology'
  }, {
    value: 'pathology', label: 'Pathology'
  }, {
    value: 'radiology', label: 'Radiology'
  } ]
  }, {
    key: 'priority', label: 'Priority', type: 'select' as const, required: true, options: [ {
    value: 'routine', label: 'Routine'
  }, {
    value: 'urgent', label: 'Urgent'
  }, {
    value: 'stat', label: 'STAT'
  } ]
  }, {
    key: 'orderDate', label: 'Order Date', type: 'date' as const, required: true
  }, {
    key: 'collectionDate', label: 'Collection Date', type: 'date' as

const
  }, {
    key: 'specimen', label: 'Specimen Type', type: 'text' as

const
  }, {
    key: 'department', label: 'Department', type: 'text' as

const
  }, {
    key: 'technician', label: 'Technician', type: 'text' as

const
  }, {
    key: 'result', label: 'Result', type: 'textarea' as

const
  }, {
    key: 'normalRange', label: 'Normal Range', type: 'text' as

const
  }, {
    key: 'comments', label: 'Comments', type: 'textarea' as

const
  } ];
  const filteredTests = labTests.filter(test => {
    const matchesSearch = test.patientName.toLowerCase().includes(searchTerm.toLowerCase()) || test.testName.toLowerCase().includes(searchTerm.toLowerCase()) || test.patientId.toLowerCase().includes(searchTerm.toLowerCase());
  const matchesFilter = selectedFilter === 'all' || test.status === selectedFilter;
  return matchesSearch && matchesFilter;
  });
  const getStatusIcon = (status: string) => {
    switch (status) {
    case 'completed': return
    <CheckCircle className="w-4 h-4 status-success" />;
  case 'cancelled': return
    <XCircle className="w-4 h-4 status-error" />;
  case 'in-progress': return
    <Clock className="w-4 h-4 status-warning" />;
  default: return
    <TestTube className="w-4 h-4 status-info" />;
  }
  };
  return ( <div className="min-h-screen bg-background p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  }
    <Card className="macos-card">
    <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 status-success-bg rounded-xl flex items-center justify-center shadow-lg">
    <TestTube className="w-6 h-6" /> </div> <div>
    <CardTitle className="text-2xl font-bold macos-text-primary"> {
    t('labTests.title')
  }
    </CardTitle> <p className="macos-text-secondary"> {
    t('labTests.subtitle')
  } </p> </div> </div>
    <Button variant="glass" onClick={
    handleCreateTest
  } className="flex items-center gap-2">
    <Plus className="w-4 h-4" /> {
    t('labTests.orderTest')
  }
    </Button> </div>
    </CardHeader>
    </Card> {/* Filters and Search */
  }
    <Card className="macos-card">
    <CardContent className="p-6"> <div className="flex flex-col md:flex-row gap-4"> <div className="flex-1"> <div className="relative">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 macos-text-tertiary w-4 h-4" />
    <Input placeholder={
    t('common.searchPlaceholder')
  } value={
    searchTerm
  } onChange={(e) => setSearchTerm(e.target.value)
  } className="pl-10 macos-input macos-focus-ring" /> </div> </div> <div className="flex items-center gap-3">
    <Filter className="w-4 h-4 macos-text-secondary" /> <select value={
    selectedFilter
  } onChange={(e) => setSelectedFilter(e.target.value)
  } className="macos-dropdown border-0 rounded-xl px-4 py-2 text-sm macos-text-primary macos-focus-ring" > <option value="all">{
    t('common.all')
  } {
    t('labTests.testResults')
  }</option> <option value="ordered">{
    t('common.ordered')
  }</option> <option value="sample-collected">{
    t('labTests.sampleCollected')
  }</option> <option value="in-progress">{
    t('common.inProgress')
  }</option> <option value="completed">{
    t('common.completed')
  }</option> <option value="cancelled">{
    t('common.cancelled')
  }</option> </select> </div> </div>
    </CardContent>
    </Card> {/* Statistics */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <Card className="macos-card">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('labTests.totalTests')
  }</p> <p className="text-2xl font-bold macos-text-primary">{
    labTests.length
  }</p> </div> <div className="w-12 h-12 status-info-bg rounded-xl flex items-center justify-center shadow-lg">
    <TestTube className="w-6 h-6" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="macos-card">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('labTests.pendingTests')
  }</p> <p className="text-2xl font-bold status-warning"> {
    labTests.filter(t => ['ordered', 'sample-collected', 'in-progress'].includes(t.status)).length
  } </p> </div> <div className="w-12 h-12 status-warning-bg rounded-xl flex items-center justify-center shadow-lg">
    <Clock className="w-6 h-6" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="macos-card">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('common.completed')
  }</p> <p className="text-2xl font-bold status-success"> {
    labTests.filter(t => t.status === 'completed').length
  } </p> </div> <div className="w-12 h-12 status-success-bg rounded-xl flex items-center justify-center shadow-lg">
    <CheckCircle className="w-6 h-6" /> </div> </div>
    </CardContent>
    </Card>
    <Card className="macos-card">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">{
    t('labTests.urgentTests')
  }</p> <p className="text-2xl font-bold status-error"> {
    labTests.filter(t => t.priority === 'urgent' || t.priority === 'stat').length
  } </p> </div> <div className="w-12 h-12 status-error-bg rounded-xl flex items-center justify-center shadow-lg">
    <AlertCircle className="w-6 h-6" /> </div> </div>
    </CardContent>
    </Card> </div> {/* Lab Tests List */
  } <div className="space-y-4"> {
    filteredTests.map((test) => (
    <Card key={
    test.id
  } className="macos-card hover:shadow-lg macos-transition">
    <CardContent className="p-6"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center gap-4 mb-4"> <div className="w-10 h-10 status-success-bg rounded-xl flex items-center justify-center shadow-lg"> {
    getStatusIcon(test.status)
  } </div> <div className="flex-1"> <h3 className="text-lg font-semibold macos-text-primary"> {
    test.testName
  } </h3> <div className="flex items-center gap-2 mt-1">
    <Badge className={
    getStatusClass(test.status)
  }> {
    test.status.replace('-', ' ')
  }
    </Badge>
    <Badge className={
    getPriorityClass(test.priority)
  }> {
    test.priority
  }
    </Badge> </div> </div> </div> <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4"> <div> <p className="text-sm macos-text-secondary">{
    t('labTests.patient')
  }: <span className="macos-text-primary font-medium">{
    test.patientName
  }</span></p> <p className="text-sm macos-text-secondary">ID: <span className="macos-text-primary font-medium">{
    test.patientId
  }</span></p> <p className="text-sm macos-text-secondary">{
    t('common.doctor')
  }: <span className="macos-text-primary font-medium">{
    test.doctorName
  }</span></p> </div> <div> <p className="text-sm macos-text-secondary">{
    t('labTests.testType')
  }: <span className="macos-text-primary font-medium">{
    test.testType
  }</span></p> <p className="text-sm macos-text-secondary">{
    t('labTests.ordered')
  }: <span className="macos-text-primary font-medium">{
    test.orderDate
  }</span></p> {
    test.sampleDate && ( <p className="text-sm macos-text-secondary">{
    t('labTests.sample')
  }: <span className="macos-text-primary font-medium">{
    test.sampleDate
  }</span></p> )
  } </div> <div> {
    test.resultDate && ( <p className="text-sm macos-text-secondary">{
    t('labTests.result')
  }: <span className="macos-text-primary font-medium">{
    test.resultDate
  }</span></p> )
  } {
    test.normalRange && ( <p className="text-sm macos-text-secondary">{
    t('labTests.normalRange')
  }: <span className="macos-text-primary font-medium">{
    test.normalRange
  }</span></p> )
  } </div> </div> {
    test.results && ( <div className="status-success-bg rounded-xl p-4 mb-4"> <p className="text-sm font-semibold macos-text-primary mb-1">{
    t('labTests.results')
  }:</p> <p className="text-sm status-success">{
    test.results
  }</p> </div> )
  } {
    test.notes && ( <div className="status-info-bg rounded-xl p-4"> <p className="text-sm font-semibold macos-text-primary mb-1">{
    t('labTests.notes')
  }:</p> <p className="text-sm status-info">{
    test.notes
  }</p> </div> )
  } </div> <div className="flex flex-col gap-2 ml-6">
    <Button variant="glass" size="sm" className="flex items-center gap-2" onClick={() => handleViewTest(test)
  } >
    <Eye className="w-4 h-4" /> {
    t('common.view')
  }
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2" onClick={() => handleEditTest(test)
  } >
    <Edit className="w-4 h-4" /> {
    t('common.edit')
  }
    </Button>
    <Button variant="glass" size="sm" className="flex items-center gap-2" onClick={() => handleDownloadReport(test)
  } >
    <Download className="w-4 h-4" /> {
    t('labTests.report')
  }
    </Button> </div> </div>
    </CardContent>
    </Card> ))
  } </div> {
    filteredTests.length === 0 && (
    <Card className="macos-card">
    <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-muted rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
    <TestTube className="w-8 h-8 macos-text-secondary" /> </div> <h3 className="text-lg font-semibold macos-text-primary mb-2">{
    t('labTests.noTestsFound')
  }</h3> <p className="macos-text-secondary"> {
    searchTerm ? t('common.adjustSearchCriteria') : t('labTests.startByOrdering')
  } </p>
    </CardContent>
    </Card> )
  } {/* CRUD Modal */
  }
    <CRUDModal isOpen={
    showModal
  } onClose={() => setShowModal(false)
  } onSubmit={
    handleSubmitTest
  } title={
    modalMode === 'create' ? 'Order New Lab Test' : 'Edit Lab Test'
  } fields={
    labTestFormFields
  } initialData={
    selectedTest || {
  }
  } mode={
    modalMode
  } /> </div> </div> );
  };
  export default LabTestManagement;
