import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  import {
    Link
  } from 'react-router-dom';
  import {
    Heart, Menu, X, ChevronDown
  } from 'lucide-react';
  import {
    Button
  } from '../ui/Button';
  import LanguageSwitcher from '../ui/LanguageSwitcher';
  import ThemeToggle from '../ui/ThemeToggle';
  const Header: React.FC = () => {
    const {
    t
  } =
  useTranslation();
  const [isMenuOpen, setIsMenuOpen] =
  useState(false);
  const [isProductDropdownOpen, setIsProductDropdownOpen] =
  useState(false);
  const navigationItems = [ {
    label: t('header.nav.home'), href: '#home'
  }, {
    label: t('header.nav.product'), href: '#product', hasDropdown: true, dropdownItems: [ {
    label: t('header.nav.features'), href: '#features'
  }, {
    label: t('header.nav.integrations'), href: '#integrations'
  }, {
    label: t('header.nav.security'), href: '#security'
  }, {
    label: t('header.nav.api'), href: '#api'
  } ]
  }, {
    label: t('header.nav.solutions'), href: '#solutions'
  }, {
    label: t('header.nav.pricing'), href: '#pricing'
  }, {
    label: t('header.nav.resources'), href: '#resources'
  }, {
    label: t('header.nav.contact'), href: '#contact'
  } ];
  return ( <header className="glass border-b border-border sticky top-0 z-50 backdrop-blur-md"> <div className="container mx-auto px-6"> <div className="flex items-center justify-between h-16"> {/* Logo */
  }
    <Link to="/" className="flex items-center space-x-2"> <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-md">
    <Heart className="w-6 h-6 text-primary-foreground" /> </div> <span className="text-2xl font-bold text-foreground">HMS AI</span>
    </Link> {/* Desktop Navigation */
  } <nav className="hidden lg:flex items-center space-x-8"> {
    navigationItems.map((item, index) => ( <div key={
    index
  } className="relative"> {
    item.hasDropdown ? ( <div className="relative" onMouseEnter={() => setIsProductDropdownOpen(true)
  } onMouseLeave={() => setIsProductDropdownOpen(false)
  } > <button className="flex items-center space-x-1 text-muted-foreground hover:text-foreground transition-colors font-medium"> <span>{
    item.label
  }</span>
    <ChevronDown className="w-4 h-4" /> </button> {
    isProductDropdownOpen && ( <div className="absolute top-full left-0 mt-2 w-48 glass rounded-lg shadow-lg border border-border py-2 z-50"> {
    item.dropdownItems?.map((dropdownItem, dropdownIndex) => ( <a key={
    dropdownIndex
  } href={
    dropdownItem.href
  } className="block px-4 py-2 text-muted-foreground hover:text-foreground hover:bg-accent transition-colors" > {
    dropdownItem.label
  } </a> ))
  } </div> )
  } </div> ) : ( <a href={
    item.href
  } className="text-muted-foreground hover:text-foreground transition-colors font-medium" > {
    item.label
  } </a> )
  } </div> ))
  } </nav> {/* Desktop Actions */
  } <div className="hidden lg:flex items-center space-x-4">
    <ThemeToggle variant="simple" />
    <LanguageSwitcher />
    <Link to="/login" className="text-muted-foreground hover:text-foreground transition-colors font-medium" > {
    t('header.signIn')
  }
    </Link>
    <Link to="/register">
    <Button className="macos-button"> {
    t('header.getStarted')
  }
    </Button>
    </Link> </div> {/* Mobile Menu Button */
  } <div className="lg:hidden flex items-center space-x-4">
    <ThemeToggle variant="simple" />
    <LanguageSwitcher /> <button onClick={() => setIsMenuOpen(!isMenuOpen)
  } className="text-muted-foreground hover:text-foreground transition-colors" > {
    isMenuOpen ?
    <X className="w-6 h-6" /> :
    <Menu className="w-6 h-6" />
  } </button> </div> </div> {/* Mobile Navigation */
  } {
    isMenuOpen && ( <div className="lg:hidden border-t border-border py-4"> <nav className="space-y-4"> {
    navigationItems.map((item, index) => ( <div key={
    index
  }> <a href={
    item.href
  } className="block text-muted-foreground hover:text-foreground transition-colors font-medium py-2" onClick={() => setIsMenuOpen(false)
  } > {
    item.label
  } </a> {
    item.hasDropdown && item.dropdownItems && ( <div className="ml-4 space-y-2 mt-2"> {
    item.dropdownItems.map((dropdownItem, dropdownIndex) => ( <a key={
    dropdownIndex
  } href={
    dropdownItem.href
  } className="block text-muted-foreground hover:text-foreground transition-colors py-1" onClick={() => setIsMenuOpen(false)
  } > {
    dropdownItem.label
  } </a> ))
  } </div> )
  } </div> ))
  } {/* Mobile Actions */
  } <div className="pt-4 border-t border-border space-y-3">
    <Link to="/login" className="block text-muted-foreground hover:text-foreground transition-colors font-medium py-2" onClick={() => setIsMenuOpen(false)
  } > {
    t('header.signIn')
  }
    </Link>
    <Link to="/register" onClick={() => setIsMenuOpen(false)
  }>
    <Button className="macos-button w-full"> {
    t('header.getStarted')
  }
    </Button>
    </Link> </div> </nav> </div> )
  } </div> </header> );
  };
  export default Header;
