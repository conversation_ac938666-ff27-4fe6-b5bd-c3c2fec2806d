const fs = require('fs');
const path = require('path');

// Critical files that need immediate fixing
const criticalFixes = [
  {
    file: 'src/components/ui/Button.tsx',
    fixes: [
      {
        from: /import \* as React from "react" import \{/g,
        to: 'import * as React from "react";\nimport {'
      },
      {
        from: /\} from "@radix-ui\/react-slot" import \{/g,
        to: '} from "@radix-ui/react-slot";\nimport {'
      },
      {
        from: /\} from "class-variance-authority" import \{/g,
        to: '} from "class-variance-authority";\nimport {'
      }
    ]
  },
  {
    file: 'src/components/ui/card.tsx',
    fixes: [
      {
        from: /import \* as React from "react" import \{/g,
        to: 'import * as React from "react";\nimport {'
      },
      {
        from: /\} from "class-variance-authority" import \{/g,
        to: '} from "class-variance-authority";\nimport {'
      }
    ]
  },
  {
    file: 'src/components/ui/badge.tsx',
    fixes: [
      {
        from: /import \* as React from "react" import \{/g,
        to: 'import * as React from "react";\nimport {'
      },
      {
        from: /\} from "class-variance-authority" import \{/g,
        to: '} from "class-variance-authority";\nimport {'
      }
    ]
  },
  {
    file: 'src/components/ui/Input.tsx',
    fixes: [
      {
        from: /import \* as React from "react" import \{/g,
        to: 'import * as React from "react";\nimport {'
      },
      {
        from: /\} from "class-variance-authority" import \{/g,
        to: '} from "class-variance-authority";\nimport {'
      }
    ]
  },
  {
    file: 'src/components/auth/LoginForm.tsx',
    fixes: [
      {
        from: /const handleChange = \(e: React\.ChangeEvent\s*<HTMLInputElement>\) => \{/g,
        to: 'const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {'
      }
    ]
  },
  {
    file: 'src/components/auth/ProtectedRoute.tsx',
    fixes: [
      {
        from: /const ProtectedRoute: React\.FC\s*<ProtectedRouteProps> = \(\{/g,
        to: 'const ProtectedRoute: React.FC<ProtectedRouteProps> = ({'
      }
    ]
  },
  {
    file: 'src/components/ui/ThemeToggle.tsx',
    fixes: [
      {
        from: /const ThemeToggle: React\.FC\s*<ThemeToggleProps> = \(\{/g,
        to: 'const ThemeToggle: React.FC<ThemeToggleProps> = ({'
      }
    ]
  },
  {
    file: 'src/components/ui/CRUDModal.tsx',
    fixes: [
      {
        from: /const CRUDModal: React\.FC\s*<CRUDModalProps> = \(\{/g,
        to: 'const CRUDModal: React.FC<CRUDModalProps> = ({'
      }
    ]
  },
  {
    file: 'src/components/ai/FloatingAIChat.tsx',
    fixes: [
      {
        from: /const FloatingAIChat: React\.FC\s*<FloatingAIChatProps> = \(\{/g,
        to: 'const FloatingAIChat: React.FC<FloatingAIChatProps> = ({'
      }
    ]
  }
];

function fixCriticalErrors() {
  console.log('🔧 Fixing critical syntax errors...\n');
  
  let fixedCount = 0;
  let errorCount = 0;
  
  for (const { file, fixes } of criticalFixes) {
    const filePath = path.join(__dirname, file);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${file}`);
      continue;
    }
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let fileChanged = false;
      
      for (const { from, to } of fixes) {
        if (content.match(from)) {
          content = content.replace(from, to);
          fileChanged = true;
        }
      }
      
      if (fileChanged) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ Fixed: ${file}`);
        fixedCount++;
      } else {
        console.log(`⚪ No changes needed: ${file}`);
      }
    } catch (error) {
      console.error(`❌ Error fixing ${file}:`, error.message);
      errorCount++;
    }
  }
  
  console.log(`\n🎉 Critical fixes complete!`);
  console.log(`✅ Successfully fixed: ${fixedCount} files`);
  if (errorCount > 0) {
    console.log(`❌ Errors encountered: ${errorCount} files`);
  }
}

// Run the script
if (require.main === module) {
  fixCriticalErrors();
}

module.exports = { fixCriticalErrors };
