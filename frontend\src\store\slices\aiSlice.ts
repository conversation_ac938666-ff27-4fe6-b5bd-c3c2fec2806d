import {
    createSlice, createAsyncThunk, type PayloadAction
  } from '@reduxjs/toolkit';
  import aiService, {
    type AIConversation, type ChatRequest, type ChatResponse, type DiagnosisRequest, type DiagnosisR<PERSON>ponse, type TreatmentRequest, type TreatmentResponse, type RiskAssessmentRequest, type RiskAssessmentResponse, type DrugInteractionRequest, type DrugInteractionResponse,
  } from '../../services/aiService';
// Async thunks export

const sendMessage = createAsyncThunk( 'ai/sendMessage', async (request: ChatRequest) => {
    const response = await aiService.sendMessage(request);
  return response;
  } );
  export

const fetchConversations = createAsyncThunk( 'ai/fetchConversations', async () => {
    const conversations = await aiService.getConversations();
  return conversations;
  } );
  export

const generateDiagnosis = createAsyncThunk( 'ai/generateDiagnosis', async (request: DiagnosisRequest) => {
    const response = await aiService.generateDiagnosis(request);
  return response;
  } );
  export

const generateTreatment = createAsyncThunk( 'ai/generateTreatment', async (request: TreatmentRequest) => {
    const response = await aiService.generateTreatment(request);
  return response;
  } );
  export

const assessRisk = createAsyncThunk( 'ai/assessRisk', async (request: RiskAssessmentRequest) => {
    const response = await aiService.assessRisk(request);
  return response;
  } );
  export

const checkDrugInteractions = createAsyncThunk( 'ai/checkDrugInteractions', async (request: DrugInteractionRequest) => {
    const response = await aiService.checkDrugInteractions(request);
  return response;
  } );
  export

const fetchHealthStatus = createAsyncThunk( 'ai/fetchHealthStatus', async () => {
    const status = await aiService.getHealthStatus();
  return status;
  } );
  export

const fetchPatientAIData = createAsyncThunk( 'ai/fetchPatientAIData', async (patientId: number) => {
    const data = await aiService.getPatientAIData(patientId);
  return {
    patientId, ...data
  };
  } ); // State interface interface AIState { // Conversations conversations: AIConversation[];
  currentConversation: AIConversation | null; // AI Responses lastChatResponse: ChatResponse | null;
  lastDiagnosis: DiagnosisResponse | null;
  lastTreatment: TreatmentResponse | null;
  lastRiskAssessment: RiskAssessmentResponse | null;
  lastDrugCheck: DrugInteractionResponse | null; // Patient-specific data patientAIData: { [patientId: number]: {
    conversations: AIConversation[];
  diagnoses: any[];
  treatments: any[];
  riskAssessments: any[];
  drugChecks: any[];
  };
  }; // Health status healthStatus: any; // Loading states isLoading: boolean;
  isSendingMessage: boolean;
  isGeneratingDiagnosis: boolean;
  isGeneratingTreatment: boolean;
  isAssessingRisk: boolean;
  isCheckingDrugs: boolean; // Error states error: string | null;
  chatError: string | null;
  diagnosisError: string | null;
  treatmentError: string | null;
  riskError: string | null;
  drugError: string | null;
  }

const initialState: AIState = {
    conversations: [], currentConversation: null, lastChatResponse: null, lastDiagnosis: null, lastTreatment: null, lastRiskAssessment: null, lastDrugCheck: null, patientAIData: {
  }, healthStatus: null, isLoading: false, isSendingMessage: false, isGeneratingDiagnosis: false, isGeneratingTreatment: false, isAssessingRisk: false, isCheckingDrugs: false, error: null, chatError: null, diagnosisError: null, treatmentError: null, riskError: null, drugError: null,
  };
  const aiSlice = createSlice({
    name: 'ai', initialState, reducers: {
    clearError: (state) => {
    state.error = null;
  state.chatError = null;
  state.diagnosisError = null;
  state.treatmentError = null;
  state.riskError = null;
  state.drugError = null;
  }, setCurrentConversation: (state, action: PayloadAction
    <AIConversation | null>) => {
    state.currentConversation = action.payload;
  }, clearLastResponses: (state) => {
    state.lastChatResponse = null;
  state.lastDiagnosis = null;
  state.lastTreatment = null;
  state.lastRiskAssessment = null;
  state.lastDrugCheck = null;
  }, updateConversationTitle: (state, action: PayloadAction<{
    conversationId: string;
  title: string
  }>) => {
    const {
    conversationId, title
  } = action.payload;
  const conversation = state.conversations.find(c => c.conversation_id === conversationId);
  if (conversation) {
    conversation.title = title;
  }
  if (state.currentConversation?.conversation_id === conversationId) {
    state.currentConversation.title = title;
  }
  }, archiveConversation: (state, action: PayloadAction<string>) => {
    const conversationId = action.payload;
  const conversation = state.conversations.find(c => c.conversation_id === conversationId);
  if (conversation) {
    conversation.is_archived = true;
  }
  }, removeConversation: (state, action: PayloadAction<string>) => {
    const conversationId = action.payload;
  state.conversations = state.conversations.filter(c => c.conversation_id !== conversationId);
  if (state.currentConversation?.conversation_id === conversationId) {
    state.currentConversation = null;
  }
  },
  }, extraReducers: (builder) => { // Send message builder .addCase(sendMessage.pending, (state) => {
    state.isSendingMessage = true;
  state.chatError = null;
  }) .addCase(sendMessage.fulfilled, (state, action) => {
    state.isSendingMessage = false;
  state.lastChatResponse = action.payload;
  state.chatError = null;
  }) .addCase(sendMessage.rejected, (state, action) => {
    state.isSendingMessage = false;
  state.chatError = action.error.message || 'Failed to send message';
  }); // Fetch conversations builder .addCase(fetchConversations.pending, (state) => {
    state.isLoading = true;
  state.error = null;
  }) .addCase(fetchConversations.fulfilled, (state, action) => {
    state.isLoading = false;
  state.conversations = action.payload;
  state.error = null;
  }) .addCase(fetchConversations.rejected, (state, action) => {
    state.isLoading = false;
  state.error = action.error.message || 'Failed to fetch conversations';
  }); // Generate diagnosis builder .addCase(generateDiagnosis.pending, (state) => {
    state.isGeneratingDiagnosis = true;
  state.diagnosisError = null;
  }) .addCase(generateDiagnosis.fulfilled, (state, action) => {
    state.isGeneratingDiagnosis = false;
  state.lastDiagnosis = action.payload;
  state.diagnosisError = null;
  }) .addCase(generateDiagnosis.rejected, (state, action) => {
    state.isGeneratingDiagnosis = false;
  state.diagnosisError = action.error.message || 'Failed to generate diagnosis';
  }); // Generate treatment builder .addCase(generateTreatment.pending, (state) => {
    state.isGeneratingTreatment = true;
  state.treatmentError = null;
  }) .addCase(generateTreatment.fulfilled, (state, action) => {
    state.isGeneratingTreatment = false;
  state.lastTreatment = action.payload;
  state.treatmentError = null;
  }) .addCase(generateTreatment.rejected, (state, action) => {
    state.isGeneratingTreatment = false;
  state.treatmentError = action.error.message || 'Failed to generate treatment';
  }); // Assess risk builder .addCase(assessRisk.pending, (state) => {
    state.isAssessingRisk = true;
  state.riskError = null;
  }) .addCase(assessRisk.fulfilled, (state, action) => {
    state.isAssessingRisk = false;
  state.lastRiskAssessment = action.payload;
  state.riskError = null;
  }) .addCase(assessRisk.rejected, (state, action) => {
    state.isAssessingRisk = false;
  state.riskError = action.error.message || 'Failed to assess risk';
  }); // Check drug interactions builder .addCase(checkDrugInteractions.pending, (state) => {
    state.isCheckingDrugs = true;
  state.drugError = null;
  }) .addCase(checkDrugInteractions.fulfilled, (state, action) => {
    state.isCheckingDrugs = false;
  state.lastDrugCheck = action.payload;
  state.drugError = null;
  }) .addCase(checkDrugInteractions.rejected, (state, action) => {
    state.isCheckingDrugs = false;
  state.drugError = action.error.message || 'Failed to check drug interactions';
  }); // Fetch health status builder .addCase(fetchHealthStatus.fulfilled, (state, action) => {
    state.healthStatus = action.payload;
  }); // Fetch patient AI data builder .addCase(fetchPatientAIData.fulfilled, (state, action) => {
    const {
    patientId, conversations, diagnoses, treatments, riskAssessments, drugChecks
  } = action.payload;
  state.patientAIData[patientId] = {
    conversations, diagnoses, treatments, riskAssessments, drugChecks,
  };
  });
  },
  });
  export

const {
    clearError, setCurrentConversation, clearLastResponses, updateConversationTitle, archiveConversation, removeConversation,
  } = aiSlice.actions;
  export default aiSlice.reducer;
