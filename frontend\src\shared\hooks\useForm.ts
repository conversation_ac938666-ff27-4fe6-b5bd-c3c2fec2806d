/** * Unified Form Hook * Provides comprehensive form state management with validation, submission, and error handling */ import {
    useState, useCallback, useEffect
  } from 'react';
  export interface FormField {
    value: any;
  error: string | null;
  touched: boolean;
  dirty: boolean;
  } export interface FormState
    <T = Record<string, any>> {
    values: T;
  errors: Record<keyof T, string | null>;
  touched: Record<keyof T, boolean>;
  dirty: Record<keyof T, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;
  } export interface ValidationRule {
    required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any, values: any) => string | null;
  } export interface UseFormOptions
    <T> {
    initialValues: T;
  validationRules?: Record<keyof T, ValidationRule>;
  onSubmit?: (values: T) => Promise<void> | void;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  resetOnSubmit?: boolean;
  } export interface UseFormResult
    <T> {
    values: T;
  errors: Record<keyof T, string | null>;
  touched: Record<keyof T, boolean>;
  dirty: Record<keyof T, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  submitCount: number;
  setValue: (field: keyof T, value: any) => void;
  setError: (field: keyof T, error: string | null) => void;
  setTouched: (field: keyof T, touched?: boolean) => void;
  validateField: (field: keyof T) => string | null;
  validateForm: () => boolean;
  handleChange: (field: keyof T) => (value: any) => void;
  handleBlur: (field: keyof T) => () => void;
  handleSubmit: (e?: React.FormEvent) => Promise<void>;
  reset: (newValues?: Partial
    <T>) => void;
  setValues: (values: Partial
    <T>) => void;
  getFieldProps: (field: keyof T) => {
    name: string;
  value: any;
  onChange: (value: any) => void;
  onBlur: () => void;
  error: string | null;
  touched: boolean;
  };
  } /** * Unified form hook with validation and state management */ export

function useForm
    <T extends Record<string, any>>( options: UseFormOptions
    <T> ): UseFormResult
    <T> {
    const {
    initialValues, validationRules = {
  }, onSubmit, validateOnChange = true, validateOnBlur = true, resetOnSubmit = false,
  } = options;
  const [state, setState] = useState
    <FormState<T>>(() => ({
    values: { ...initialValues
  }, errors: Object.keys(initialValues).reduce((acc, key) => ({ ...acc, [key]: null
  }), {
  } as Record<keyof T, string | null>), touched: Object.keys(initialValues).reduce((acc, key) => ({ ...acc, [key]: false
  }), {
  } as Record<keyof T, boolean>), dirty: Object.keys(initialValues).reduce((acc, key) => ({ ...acc, [key]: false
  }), {
  } as Record<keyof T, boolean>), isValid: true, isSubmitting: false, submitCount: 0,
  })); // Validate a single field

const validateField =
  useCallback((field: keyof T): string | null => {
    const value = state.values[field];
  const rules = validationRules[field];
  if (!rules) return null; // Required validation
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return `${
    String(field)
  } is required`;
  } // Skip other validations if field is empty and not required
  if (!value && !rules.required) return null; // String validations
  if (typeof value === 'string') {
    if (rules.minLength && value.length < rules.minLength) {
    return `${
    String(field)
  } must be at least ${
    rules.minLength
  } characters`;
  }
  if (rules.maxLength && value.length > rules.maxLength) {
    return `${
    String(field)
  } must be no more than ${
    rules.maxLength
  } characters`;
  }
  if (rules.pattern && !rules.pattern.test(value)) {
    return `${
    String(field)
  } format is invalid`;
  }
  } // Custom validation
  if (rules.custom) {
    return rules.custom(value, state.values);
  } return null;
  }, [state.values, validationRules]); // Validate entire form

const validateForm =
  useCallback((): boolean => {
    const newErrors: Record<keyof T, string | null> = {
  } as Record<keyof T, string | null>;
  let isValid = true;
  Object.keys(state.values).forEach((field) => {
    const error = validateField(field as keyof T);
  newErrors[field as keyof T] = error;
  if (error) isValid = false;
  });
  setState(prev => ({ ...prev, errors: newErrors, isValid,
  }));
  return isValid;
  }, [state.values, validateField]); // Set field value

const setValue =
  useCallback((field: keyof T, value: any) => {
    setState(prev => {
    const newValues = { ...prev.values, [field]: value
  };
  const newState = { ...prev, values: newValues, dirty: { ...prev.dirty, [field]: true
  },
  }; // Validate on change if enabled
  if (validateOnChange) {
    const error = validateField(field);
  newState.errors = { ...prev.errors, [field]: error
  };
  newState.isValid = Object.values({ ...newState.errors, [field]: error
  }).every(err => !err);
  } return newState;
  });
  }, [validateField, validateOnChange]); // Set field error

const setError =
  useCallback((field: keyof T, error: string | null) => {
    setState(prev => ({ ...prev, errors: { ...prev.errors, [field]: error
  }, isValid: Object.values({ ...prev.errors, [field]: error
  }).every(err => !err),
  }));
  }, []); // Set field touched

const setTouched =
  useCallback((field: keyof T, touched = true) => {
    setState(prev => {
    const newState = { ...prev, touched: { ...prev.touched, [field]: touched
  },
  }; // Validate on blur if enabled and field is touched
  if (validateOnBlur && touched) {
    const error = validateField(field);
  newState.errors = { ...prev.errors, [field]: error
  };
  newState.isValid = Object.values({ ...newState.errors, [field]: error
  }).every(err => !err);
  } return newState;
  });
  }, [validateField, validateOnBlur]); // Handle field change

const handleChange =
  useCallback((field: keyof T) => (value: any) => {
    setValue(field, value);
  }, [setValue]); // Handle field blur

const handleBlur =
  useCallback((field: keyof T) => () => {
    setTouched(field, true);
  }, [setTouched]); // Handle form submission

const handleSubmit =
  useCallback(async (e?: React.FormEvent) => {
    if (e) {
    e.preventDefault();
  } setState(prev => ({ ...prev, isSubmitting: true, submitCount: prev.submitCount + 1
  }));
  try { // Mark all fields as touched

const allTouched = Object.keys(state.values).reduce( (acc, key) => ({ ...acc, [key]: true
  }), {
  } as Record<keyof T, boolean> );
  setState(prev => ({ ...prev, touched: allTouched
  })); // Validate form

const isValid = validateForm();
  if (isValid && onSubmit) {
    await onSubmit(state.values);
  if (resetOnSubmit) {
    reset();
  }
  }
  } catch (error) {
    console.error('Form submission error:', error);
  } finally {
    setState(prev => ({ ...prev, isSubmitting: false
  }));
  }
  }, [state.values, validateForm, onSubmit, resetOnSubmit]); // Reset form

const reset =
  useCallback((newValues?: Partial
    <T>) => {
    const resetValues = newValues ? { ...initialValues, ...newValues
  } : initialValues;
  setState({
    values: resetValues, errors: Object.keys(resetValues).reduce((acc, key) => ({ ...acc, [key]: null
  }), {
  } as Record<keyof T, string | null>), touched: Object.keys(resetValues).reduce((acc, key) => ({ ...acc, [key]: false
  }), {
  } as Record<keyof T, boolean>), dirty: Object.keys(resetValues).reduce((acc, key) => ({ ...acc, [key]: false
  }), {
  } as Record<keyof T, boolean>), isValid: true, isSubmitting: false, submitCount: 0,
  });
  }, [initialValues]); // Set multiple values

const setValues =
  useCallback((values: Partial
    <T>) => {
    setState(prev => ({ ...prev, values: { ...prev.values, ...values
  }, dirty: Object.keys(values).reduce((acc, key) => ({ ...acc, [key]: true
  }), prev.dirty),
  }));
  }, []); // Get field props for easy integration with form components

const getFieldProps =
  useCallback((field: keyof T) => ({
    name: String(field), value: state.values[field], onChange: handleChange(field), onBlur: handleBlur(field), error: state.errors[field], touched: state.touched[field],
  }), [state.values, state.errors, state.touched, handleChange, handleBlur]);
  return {
    values: state.values, errors: state.errors, touched: state.touched, dirty: state.dirty, isValid: state.isValid, isSubmitting: state.isSubmitting, submitCount: state.submitCount, setValue, setError, setTouched, validateField, validateForm, handleChange, handleBlur, handleSubmit, reset, setValues, getFieldProps,
  };
  }
