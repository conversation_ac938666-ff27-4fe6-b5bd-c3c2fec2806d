/** * Toast Notification Hook * Provides unified toast notification system with queue management */ import {
    useState, useCallback, useEffect, useRef
  } from 'react';
  export interface Toast {
    id: string;
  title?: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
  onClick: () => void;
  };
  onClose?: () => void;
  createdAt: number;
  } export interface ToastOptions {
    title?: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
  onClick: () => void;
  };
  onClose?: () => void;
  } export interface UseToastResult {
    toasts: Toast[];
  addToast: (message: string, type: Toast['type'], options?: ToastOptions) => string;
  removeToast: (id: string) => void;
  clearAll: () => void;
  success: (message: string, options?: ToastOptions) => string;
  error: (message: string, options?: ToastOptions) => string;
  warning: (message: string, options?: ToastOptions) => string;
  info: (message: string, options?: ToastOptions) => string;
  updateToast: (id: string, updates: Partial
    <Toast>) => void;
  }

const DEFAULT_DURATION = 5000; // 5 seconds

const MAX_TOASTS = 5; /** * Hook for managing toast notifications */ export

function
  useToast(): UseToastResult {
    const [toasts, setToasts] = useState
    <Toast[]>([]);
  const timeoutRefs = useRef
    <Map<string, NodeJS.Timeout>>(new Map()); // Generate unique ID for toasts

const generateId =
  useCallback(() => {
    return `toast-${
    Date.now()
  }-${
    Math.random().toString(36).substr(2, 9)
  }`;
  }, []); // Remove toast by ID

const removeToast =
  useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id)); // Clear timeout if exists

const timeout = timeoutRefs.current.get(id);
  if (timeout) {
    clearTimeout(timeout);
  timeoutRefs.current.delete(id);
  }
  }, []); // Add new toast

const addToast =
  useCallback(( message: string, type: Toast['type'], options: ToastOptions = {
  } ): string => {
    const id = generateId();
  const duration = options.duration ?? DEFAULT_DURATION;
  const newToast: Toast = {
    id, title: options.title, message, type, duration, persistent: options.persistent || false, action: options.action, onClose: options.onClose, createdAt: Date.now(),
  };
  setToasts(prev => { // Remove oldest toast if we've reached the maximum

const updatedToasts = prev.length >= MAX_TOASTS ? prev.slice(1) : prev;
  return [...updatedToasts, newToast];
  }); // Set auto-remove timeout if not persistent
  if (!newToast.persistent && duration > 0) {
    const timeout = setTimeout(() => {
    removeToast(id);
  options.onClose?.();
  }, duration);
  timeoutRefs.current.set(id, timeout);
  } return id;
  }, [generateId, removeToast]); // Update existing toast

const updateToast =
  useCallback((id: string, updates: Partial
    <Toast>) => {
    setToasts(prev => prev.map(toast => toast.id === id ? { ...toast, ...updates
  } : toast ));
  }, []); // Clear all toasts

const clearAll =
  useCallback(() => { // Clear all timeouts timeoutRefs.current.forEach(timeout => clearTimeout(timeout));
  timeoutRefs.current.clear();
  setToasts([]);
  }, []); // Convenience methods for different toast types

const success =
  useCallback((message: string, options?: ToastOptions) => {
    return addToast(message, 'success', options);
  }, [addToast]);
  const error =
  useCallback((message: string, options?: ToastOptions) => {
    return addToast(message, 'error', {
    duration: 8000, // Errors stay longer by default ...options
  });
  }, [addToast]);
  const warning =
  useCallback((message: string, options?: ToastOptions) => {
    return addToast(message, 'warning', options);
  }, [addToast]);
  const info =
  useCallback((message: string, options?: ToastOptions) => {
    return addToast(message, 'info', options);
  }, [addToast]); // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
    timeoutRefs.current.forEach(timeout => clearTimeout(timeout));
  timeoutRefs.current.clear();
  };
  }, []);
  return {
    toasts, addToast, removeToast, clearAll, success, error, warning, info, updateToast,
  };
  } /** * Hook for toast notifications with global state management */

let globalToastState: UseToastResult | null = null;
  export

function
  useGlobalToast(): UseToastResult {
    const localToast =
  useToast(); // Initialize global state if it doesn't exist
  if (!globalToastState) {
    globalToastState = localToast;
  } return globalToastState;
  } /** * Hook for API operation toasts with automatic success/error handling */ export

function
  useApiToast() {
    const toast =
  useToast();
  const handleApiOperation =
  useCallback(async
    <T>( operation: () => Promise
    <T>, options: {
    loadingMessage?: string;
  successMessage?: string;
  errorMessage?: string;
  showSuccess?: boolean;
  showError?: boolean;
  } = {
  } ): Promise
    <T> => {
    const {
    loadingMessage = 'Processing...', successMessage = 'Operation completed successfully', errorMessage = 'Operation failed', showSuccess = true, showError = true,
  } = options;
  let loadingToastId: string | null = null;
  try { // Show loading toast
  if (loadingMessage) {
    loadingToastId = toast.info(loadingMessage, {
    persistent: true
  });
  } // Execute operation

const result = await operation(); // Remove loading toast
  if (loadingToastId) {
    toast.removeToast(loadingToastId);
  } // Show success toast
  if (showSuccess) {
    toast.success(successMessage);
  } return result;
  } catch (error) { // Remove loading toast
  if (loadingToastId) {
    toast.removeToast(loadingToastId);
  } // Show error toast
  if (showError) {
    const message = error instanceof Error ? error.message : errorMessage;
  toast.error(message);
  } throw error;
  }
  }, [toast]);
  return { ...toast, handleApiOperation,
  };
  } /** * Hook for form submission toasts */ export

function
  useFormToast() {
    const toast =
  useToast();
  const handleFormSubmission =
  useCallback(async
    <T>( submitFunction: () => Promise
    <T>, options: {
    successMessage?: string;
  errorMessage?: string;
  validationErrorMessage?: string;
  } = {
  } ): Promise
    <T | null> => {
    const {
    successMessage = 'Form submitted successfully', errorMessage = 'Failed to submit form', validationErrorMessage = 'Please check your input and try again',
  } = options;
  try {
    const result = await submitFunction();
  toast.success(successMessage);
  return result;
  } catch (error) {
    if (error instanceof Error) { // Check if it's a validation error
  if (error.message.includes('validation') || error.message.includes('invalid')) {
    toast.warning(validationErrorMessage);
  } else {
    toast.error(error.message);
  }
  } else {
    toast.error(errorMessage);
  } return null;
  }
  }, [toast]);
  return { ...toast, handleFormSubmission,
  };
  } /** * Hook for batch operations with progress toasts */ export

function
  useBatchToast() {
    const toast =
  useToast();
  const handleBatchOperation =
  useCallback(async
    <T>( items: T[], operation: (item: T, index: number) => Promise<void>, options: {
    batchName?: string;
  showProgress?: boolean;
  showSummary?: boolean;
  } = {
  } ) => {
    const {
    batchName = 'items', showProgress = true, showSummary = true,
  } = options;
  let progressToastId: string | null = null;
  let completed = 0;
  let failed = 0;
  try {
    if (showProgress) {
    progressToastId = toast.info( `Processing ${
    batchName
  }: 0/${
    items.length
  }`, {
    persistent: true
  } );
  } for (let i = 0;
  i < items.length;
  i++) {
    try {
    await operation(items[i], i);
  completed++;
  } catch (error) {
    failed++;
  console.error(`Failed to process ${
    batchName
  } at index ${
    i
  }:`, error);
  } // Update progress
  if (progressToastId && showProgress) {
    toast.updateToast(progressToastId, {
    message: `Processing ${
    batchName
  }: ${
    completed + failed
  }/${
    items.length
  }`,
  });
  }
  } // Remove progress toast
  if (progressToastId) {
    toast.removeToast(progressToastId);
  } // Show summary
  if (showSummary) {
    if (failed === 0) {
    toast.success(`Successfully processed all ${
    completed
  } ${
    batchName
  }`);
  } else
  if (completed === 0) {
    toast.error(`Failed to process all ${
    failed
  } ${
    batchName
  }`);
  } else {
    toast.warning( `Processed ${
    completed
  } ${
    batchName
  } successfully, ${
    failed
  } failed` );
  }
  } return {
    completed, failed, total: items.length
  };
  } catch (error) {
    if (progressToastId) {
    toast.removeToast(progressToastId);
  } toast.error(`Batch operation failed: ${
    error instanceof Error ? error.message : 'Unknown error'
  }`);
  throw error;
  }
  }, [toast]);
  return { ...toast, handleBatchOperation,
  };
  }
