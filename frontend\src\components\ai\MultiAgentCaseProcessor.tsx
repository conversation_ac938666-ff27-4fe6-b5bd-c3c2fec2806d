import React, {
    useState
  } from 'react';
  import {
    <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, Clock, Brain, Steth<PERSON>cope, Pill, Activity, Loader2, FileText, TrendingUp
  } from 'lucide-react';
  interface CaseData {
    patient_id: number;
  symptoms: string[];
  urgency_level: 'low' | 'medium' | 'high' | 'critical';
  case_type: 'standard' | 'emergency' | 'complex' | 'consultation';
  case_description: string;
  } interface AgentResult {
    agent: string;
  confidence_score?: number; [key: string]: any;
  } interface CaseResult {
    success: boolean;
  case_id: string;
  workflow_completed: boolean;
  agent_results: Record<string, AgentResult>;
  final_recommendation: any;
  confidence_scores: Record<string, number>;
  active_agents: string[];
  }

const MultiAgentCaseProcessor: React.FC = () => {
    const [caseData, setCaseData] = useState
    <CaseData>({
    patient_id: 0, symptoms: [''], urgency_level: 'medium', case_type: 'standard', case_description: ''
  });
  const [isProcessing, setIsProcessing] =
  useState(false);
  const [result, setResult] = useState
    <CaseResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [processingSteps, setProcessingSteps] = useState<string[]>([]);
  const addSymptom = () => {
    setCaseData(prev => ({ ...prev, symptoms: [...prev.symptoms, '']
  }));
  };
  const removeSymptom = (index: number) => {
    if (caseData.symptoms.length > 1) {
    setCaseData(prev => ({ ...prev, symptoms: prev.symptoms.filter((_, i) => i !== index)
  }));
  }
  };
  const updateSymptom = (index: number, value: string) => {
    setCaseData(prev => ({ ...prev, symptoms: prev.symptoms.map((symptom, i) => i === index ? value : symptom)
  }));
  };
  const processCase = async () => {
    if (!caseData.patient_id || caseData.symptoms.every(s => !s.trim())) {
    setError('Please select a patient and enter at least one symptom');
  return;
  } setIsProcessing(true);
  setError(null);
  setResult(null);
  setProcessingSteps([]);
  try { // Simulate processing steps

const steps = [ 'Initializing multi-agent system...', 'Triage agent assessing urgency...', 'Diagnostic agent analyzing symptoms...', 'Treatment agent developing plan...', 'Pharmacist agent checking medications...', 'Coordinator agent synthesizing results...' ];
  for (let i = 0;
  i < steps.length;
  i++) {
    setProcessingSteps(prev => [...prev, steps[i]]);
  await new Promise(resolve => setTimeout(resolve, 1000));
  }

const token = localStorage.getItem('token');
  const response = await fetch('/api/ai/multi-agent/cases/process_case/', {
    method: 'POST', headers: { 'Authorization': `Bearer ${
    token
  }`, 'Content-Type': 'application/json'
  }, body: JSON.stringify({ ...caseData, symptoms: caseData.symptoms.filter(s => s.trim())
  })
  });
  if (response.ok) {
    const data = await response.json();
  setResult(data);
  setProcessingSteps(prev => [...prev, 'Multi-agent processing completed!']);
  } else {
    const errorData = await response.json();
  setError(errorData.error || 'Failed to process case');
  }
  } catch (err) {
    setError('Network error occurred');
  console.error('Case processing error:', err);
  } finally {
    setIsProcessing(false);
  }
  };
  const getAgentIcon = (agentRole: string) => {
    switch (agentRole) {
    case 'triage': return
    <Activity className="w-4 h-4" />;
  case 'diagnostic': return
    <Stethoscope className="w-4 h-4" />;
  case 'treatment': return
    <Brain className="w-4 h-4" />;
  case 'pharmacist': return
    <Pill className="w-4 h-4" />;
  case 'coordinator': return
    <Users className="w-4 h-4" />;
  default: return
    <Brain className="w-4 h-4" />;
  }
  };
  const getUrgencyColor = (level: string) => {
    switch (level) {
    case 'critical': return 'status-error ';
  case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
  case 'medium': return 'status-warning ';
  case 'low': return 'status-success ';
  default: return 'bg-muted text-foreground border-border';
  }
  };
  return ( <div className="space-y-6"> {/* Header */
  } <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg p-6 text-white"> <div className="flex items-center gap-3">
    <Users className="w-8 h-8" /> <div> <h2 className="text-2xl font-bold">Multi-Agent Case Processor</h2> <p className="text-indigo-100"> Process medical cases through specialized AI agents working in coordination </p> </div> </div> </div> {/* Case Input Form */
  } <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <h3 className="text-lg font-semibold text-foreground dark:text-white mb-4"> Case Information </h3> <div className="grid grid-cols-1 md:grid-cols-2 gap-6"> {/* Patient Selection */
  } <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-2"> Patient * </label> <select value={
    caseData.patient_id
  } onChange={(e) => setCaseData(prev => ({ ...prev, patient_id: Number(e.target.value)
  }))
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" > <option value={0
  }>Select a patient...</option> <option value={1
  }>John Doe (P000001)</option> <option value={2
  }>Jane Smith (P000002)</option> <option value={3
  }>Mike Johnson (P000003)</option> </select> </div> {/* Case Type */
  } <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-2"> Case Type </label> <select value={
    caseData.case_type
  } onChange={(e) => setCaseData(prev => ({ ...prev, case_type: e.target.value as any
  }))
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" > <option value="standard">Standard Care</option> <option value="emergency">Emergency Care</option> <option value="complex">Complex Case</option> <option value="consultation">Multi-specialist Consultation</option> </select> </div> {/* Urgency Level */
  } <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-2"> Urgency Level </label> <select value={
    caseData.urgency_level
  } onChange={(e) => setCaseData(prev => ({ ...prev, urgency_level: e.target.value as any
  }))
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" > <option value="low">Low</option> <option value="medium">Medium</option> <option value="high">High</option> <option value="critical">Critical</option> </select> </div> </div> {/* Symptoms */
  } <div className="mt-6"> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-2"> Symptoms * </label> <div className="space-y-2"> {
    caseData.symptoms.map((symptom, index) => ( <div key={
    index
  } className="flex gap-2"> <input type="text" value={
    symptom
  } onChange={(e) => updateSymptom(index, e.target.value)
  } placeholder={`Symptom ${
    index + 1
  }`
  } className="flex-1 px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> {
    caseData.symptoms.length > 1 && ( <button onClick={() => removeSymptom(index)
  } className="px-3 py-2 text-rose-700 dark:text-rose-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors" > Remove </button> )
  } </div> ))
  } <button onClick={
    addSymptom
  } className="text-indigo-600 hover:text-indigo-700 text-sm font-medium" > + Add Another Symptom </button> </div> </div> {/* Case Description */
  } <div className="mt-6"> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-2"> Case Description </label> <textarea value={
    caseData.case_description
  } onChange={(e) => setCaseData(prev => ({ ...prev, case_description: e.target.value
  }))
  } placeholder="Additional details about the case..." rows={3
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> </div> {/* Process Button */
  } <div className="mt-6"> <button onClick={
    processCase
  } disabled={
    isProcessing || !caseData.patient_id || caseData.symptoms.every(s => !s.trim())
  } className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors" > {
    isProcessing ? ( <>
    <Loader2 className="w-5 h-5 animate-spin" /> Processing through Multi-Agent System... </> ) : ( <>
    <Play className="w-5 h-5" /> Process Case </> )
  } </button> </div> </div> {/* Processing Steps */
  } {
    processingSteps.length > 0 && ( <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <h3 className="text-lg font-semibold text-foreground dark:text-white mb-4"> Processing Status </h3> <div className="space-y-2"> {
    processingSteps.map((step, index) => ( <div key={
    index
  } className="flex items-center gap-3">
    <CheckCircle className="w-4 h-4 text-emerald-700 dark:text-emerald-400" /> <span className="text-sm text-foreground dark:text-gray-300">{
    step
  }</span> </div> ))
  } </div> </div> )
  } {/* Error Display */
  } {
    error && ( <div className="bg-red-50 dark:bg-red-900/20 border rounded-lg p-4"> <div className="flex items-center gap-2">
    <AlertTriangle className="w-5 h-5 text-rose-700 dark:text-rose-400 dark:text-red-400" /> <p className="text-rose-700 dark:text-rose-400 dark:text-red-400 font-medium">Error</p> </div> <p className="text-rose-700 dark:text-rose-400 dark:text-red-400 text-sm mt-1">{
    error
  }</p> </div> )
  } {/* Results Display */
  } {
    result && result.success && ( <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <div className="flex items-center gap-3 mb-6">
    <CheckCircle className="w-6 h-6 text-emerald-700 dark:text-emerald-400" /> <h3 className="text-lg font-semibold text-foreground dark:text-white"> Multi-Agent Analysis Complete </h3> </div> {/* Case Summary */
  } <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border "> <div className="flex items-center justify-between mb-2"> <span className="font-medium text-emerald-700 dark:text-emerald-400 dark:text-green-200">Case ID:</span> <span className="text-emerald-700 dark:text-emerald-400 dark:text-green-300">{
    result.case_id
  }</span> </div> <div className="flex items-center justify-between"> <span className="font-medium text-emerald-700 dark:text-emerald-400 dark:text-green-200">Active Agents:</span> <span className="text-emerald-700 dark:text-emerald-400 dark:text-green-300">{
    result.active_agents.length
  }</span> </div> </div> {/* Agent Results */
  } <div className="space-y-4"> <h4 className="font-medium text-foreground dark:text-white">Agent Results:</h4> {
    Object.entries(result.agent_results).map(([agentRole, agentResult]) => ( <div key={
    agentRole
  } className="border border-border dark:border-gray-600 rounded-lg p-4" > <div className="flex items-center gap-3 mb-3"> <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-lg"> {
    getAgentIcon(agentRole)
  } </div> <div className="flex-1"> <h5 className="font-medium text-foreground dark:text-white capitalize"> {
    agentRole
  } Agent </h5> {
    agentResult.confidence_score && ( <p className="text-sm text-muted-foreground dark:text-gray-400"> Confidence: {(agentResult.confidence_score * 100).toFixed(1)
  }% </p> )
  } </div> </div> <div className="text-sm text-foreground dark:text-gray-300"> <pre className="whitespace-pre-wrap font-mono text-xs bg-muted dark:bg-gray-700 p-3 rounded"> {
    JSON.stringify(agentResult, null, 2)
  } </pre> </div> </div> ))
  } </div> {/* Final Recommendation */
  } {
    result.final_recommendation && ( <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border "> <h4 className="font-medium text-sky-700 dark:text-sky-400 dark:text-blue-200 mb-2"> Final Coordinated Recommendation: </h4> <div className="text-sm text-sky-700 dark:text-sky-400 dark:text-blue-300"> <pre className="whitespace-pre-wrap"> {
    JSON.stringify(result.final_recommendation, null, 2)
  } </pre> </div> </div> )
  } </div> )
  } </div> );
  };
  export default MultiAgentCaseProcessor;
