import React, {
    useState
  } from 'react';
  import {
    AlertTriangle, Heart, Zap, Activity, Phone, Clock, Users, CheckCircle, Loader2
  } from 'lucide-react';
  interface EmergencyData {
    emergency_type: string;
  patient_id: number;
  vitals: {
    blood_pressure_systolic?: number;
  blood_pressure_diastolic?: number;
  heart_rate?: number;
  respiratory_rate?: number;
  oxygen_saturation?: number;
  temperature?: number;
  };
  description: string;
  } interface EmergencyResponse {
    success: boolean;
  case_id: string;
  emergency_response: any;
  protocols_activated: string[];
  immediate_interventions: string[];
  team_activation: string[];
  }

const EmergencyActivation: React.FC = () => {
    const [emergencyData, setEmergencyData] = useState
    <EmergencyData>({
    emergency_type: '', patient_id: 0, vitals: {
  }, description: ''
  });
  const [isActivating, setIsActivating] =
  useState(false);
  const [response, setResponse] = useState
    <EmergencyResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const emergencyTypes = [ {
    value: 'cardiac', label: 'Cardiac Emergency', icon:
    <Heart className="w-4 h-4" />, color: 'red'
  }, {
    value: 'respiratory', label: 'Respiratory Emergency', icon:
    <Activity className="w-4 h-4" />, color: 'blue'
  }, {
    value: 'trauma', label: 'Trauma Emergency', icon:
    <AlertTriangle className="w-4 h-4" />, color: 'orange'
  }, {
    value: 'stroke', label: 'Stroke Emergency', icon:
    <Zap className="w-4 h-4" />, color: 'purple'
  }, {
    value: 'sepsis', label: 'Sepsis Emergency', icon:
    <Activity className="w-4 h-4" />, color: 'yellow'
  }, {
    value: 'overdose', label: 'Overdose Emergency', icon:
    <AlertTriangle className="w-4 h-4" />, color: 'green'
  } ];
  const activateEmergency = async () => {
    if (!emergencyData.emergency_type || !emergencyData.patient_id) {
    setError('Please select emergency type and patient');
  return;
  } setIsActivating(true);
  setError(null);
  setResponse(null);
  try {
    const token = localStorage.getItem('token');
  const apiResponse = await fetch('/api/ai/multi-agent/cases/emergency_activation/', {
    method: 'POST', headers: { 'Authorization': `Bearer ${
    token
  }`, 'Content-Type': 'application/json'
  }, body: JSON.stringify(emergencyData)
  });
  if (apiResponse.ok) {
    const data = await apiResponse.json();
  setResponse(data);
  } else {
    const errorData = await apiResponse.json();
  setError(errorData.error || 'Failed to activate emergency protocols');
  }
  } catch (err) {
    setError('Network error occurred');
  console.error('Emergency activation error:', err);
  } finally {
    setIsActivating(false);
  }
  };
  const updateVitals = (field: string, value: string) => {
    setEmergencyData(prev => ({ ...prev, vitals: { ...prev.vitals, [field]: value ? parseFloat(value) : undefined
  }
  }));
  };
  const getEmergencyTypeColor = (type: string) => {
    const emergency = emergencyTypes.find(e => e.value === type);
  return emergency?.color || 'gray';
  };
  return ( <div className="space-y-6"> {/* Emergency Header */
  } <div className="bg-gradient-to-r from-red-600 to-orange-600 rounded-lg p-6 text-white"> <div className="flex items-center gap-3">
    <AlertTriangle className="w-8 h-8" /> <div> <h2 className="text-2xl font-bold">Emergency Protocol Activation</h2> <p className="text-red-100"> Rapid response system for critical medical emergencies </p> </div> </div> </div> {/* Emergency Type Selection */
  } <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <h3 className="text-lg font-semibold text-foreground dark:text-white mb-4"> Emergency Type </h3> <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"> {
    emergencyTypes.map((type) => ( <button key={
    type.value
  } onClick={() => setEmergencyData(prev => ({ ...prev, emergency_type: type.value
  }))
  } className={`p-4 rounded-lg border-2 transition-all ${
    emergencyData.emergency_type === type.value ? `border-${
    type.color
  }-500 bg-${
    type.color
  }-50 dark:bg-${
    type.color
  }-900/20` : 'border-border dark:border-gray-600 hover:border-border dark:hover:border-gray-500'
  }`
  } > <div className="flex items-center gap-3"> <div className={`p-2 rounded-lg bg-${
    type.color
  }-100 dark:bg-${
    type.color
  }-900 text-${
    type.color
  }-600 dark:text-${
    type.color
  }-400`
  }> {
    type.icon
  } </div> <span className="font-medium text-foreground dark:text-white"> {
    type.label
  } </span> </div> </button> ))
  } </div> </div> {/* Patient and Vitals */
  } <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> {/* Patient Selection */
  } <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <h3 className="text-lg font-semibold text-foreground dark:text-white mb-4"> Patient Information </h3> <div className="space-y-4"> <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-2"> Patient * </label> <select value={
    emergencyData.patient_id
  } onChange={(e) => setEmergencyData(prev => ({ ...prev, patient_id: Number(e.target.value)
  }))
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" > <option value={0
  }>Select patient...</option> <option value={1
  }>John Doe (P000001)</option> <option value={2
  }>Jane Smith (P000002)</option> <option value={3
  }>Mike Johnson (P000003)</option> </select> </div> <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-2"> Emergency Description </label> <textarea value={
    emergencyData.description
  } onChange={(e) => setEmergencyData(prev => ({ ...prev, description: e.target.value
  }))
  } placeholder="Describe the emergency situation..." rows={3
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> </div> </div> </div> {/* Vital Signs */
  } <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <h3 className="text-lg font-semibold text-foreground dark:text-white mb-4"> Vital Signs </h3> <div className="grid grid-cols-2 gap-4"> <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-1"> BP Systolic </label> <input type="number" placeholder="120" onChange={(e) => updateVitals('blood_pressure_systolic', e.target.value)
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> </div> <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-1"> BP Diastolic </label> <input type="number" placeholder="80" onChange={(e) => updateVitals('blood_pressure_diastolic', e.target.value)
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> </div> <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-1"> Heart Rate </label> <input type="number" placeholder="72" onChange={(e) => updateVitals('heart_rate', e.target.value)
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> </div> <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-1"> Respiratory Rate </label> <input type="number" placeholder="16" onChange={(e) => updateVitals('respiratory_rate', e.target.value)
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> </div> <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-1"> O2 Saturation (%) </label> <input type="number" placeholder="98" onChange={(e) => updateVitals('oxygen_saturation', e.target.value)
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> </div> <div> <label className="block text-sm font-medium text-foreground dark:text-gray-300 mb-1"> Temperature (°F) </label> <input type="number" step="0.1" placeholder="98.6" onChange={(e) => updateVitals('temperature', e.target.value)
  } className="w-full px-3 py-2 border border-border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent bg-background dark:bg-gray-700 text-foreground dark:text-white" /> </div> </div> </div> </div> {/* Activation Button */
  } <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <button onClick={
    activateEmergency
  } disabled={
    isActivating || !emergencyData.emergency_type || !emergencyData.patient_id
  } className="w-full flex items-center justify-center gap-3 px-6 py-4 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-lg font-semibold" > {
    isActivating ? ( <>
    <Loader2 className="w-6 h-6 animate-spin" /> Activating Emergency Protocols... </> ) : ( <>
    <AlertTriangle className="w-6 h-6" /> ACTIVATE EMERGENCY PROTOCOLS </> )
  } </button> </div> {/* Error Display */
  } {
    error && ( <div className="bg-red-50 dark:bg-red-900/20 border rounded-lg p-4"> <div className="flex items-center gap-2">
    <AlertTriangle className="w-5 h-5 text-rose-700 dark:text-rose-400 dark:text-red-400" /> <p className="text-rose-700 dark:text-rose-400 dark:text-red-400 font-medium">Error</p> </div> <p className="text-rose-700 dark:text-rose-400 dark:text-red-400 text-sm mt-1">{
    error
  }</p> </div> )
  } {/* Emergency Response */
  } {
    response && response.success && ( <div className="bg-background dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-border dark:border-gray-700"> <div className="flex items-center gap-3 mb-6">
    <CheckCircle className="w-6 h-6 text-emerald-700 dark:text-emerald-400" /> <h3 className="text-lg font-semibold text-foreground dark:text-white"> Emergency Protocols Activated </h3> </div> <div className="grid grid-cols-1 md:grid-cols-3 gap-6"> {/* Protocols Activated */
  } <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4"> <h4 className="font-medium text-sky-700 dark:text-sky-400 dark:text-blue-200 mb-3 flex items-center gap-2">
    <Activity className="w-4 h-4" /> Protocols Activated </h4> <ul className="space-y-1"> {
    response.protocols_activated.map((protocol, index) => ( <li key={
    index
  } className="text-sm text-sky-700 dark:text-sky-400 dark:text-blue-300"> • {
    protocol
  } </li> ))
  } </ul> </div> {/* Immediate Interventions */
  } <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4"> <h4 className="font-medium text-orange-800 dark:text-orange-200 mb-3 flex items-center gap-2">
    <Clock className="w-4 h-4" /> Immediate Interventions </h4> <ul className="space-y-1"> {
    response.immediate_interventions.map((intervention, index) => ( <li key={
    index
  } className="text-sm text-orange-700 dark:text-orange-300"> • {
    intervention
  } </li> ))
  } </ul> </div> {/* Team Activation */
  } <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4"> <h4 className="font-medium text-emerald-700 dark:text-emerald-400 dark:text-green-200 mb-3 flex items-center gap-2">
    <Users className="w-4 h-4" /> Team Activation </h4> <ul className="space-y-1"> {
    response.team_activation.map((team, index) => ( <li key={
    index
  } className="text-sm text-emerald-700 dark:text-emerald-400 dark:text-green-300"> • {
    team
  } </li> ))
  } </ul> </div> </div> <div className="mt-6 p-4 bg-muted dark:bg-gray-700 rounded-lg"> <p className="text-sm text-muted-foreground dark:text-gray-400"> <strong>Case ID:</strong> {
    response.case_id
  } </p> <p className="text-sm text-muted-foreground dark:text-gray-400 mt-1"> Emergency response protocols have been activated. Medical teams have been notified. </p> </div> </div> )
  } </div> );
  };
  export default EmergencyActivation;
