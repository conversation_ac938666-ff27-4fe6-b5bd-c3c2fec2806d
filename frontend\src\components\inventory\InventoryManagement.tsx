import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Input } from '../ui/Input';
import DataTable from '../ui/DataTable';
import CRUDModal from '../ui/CRUDModal';
import { useApi } from '../../shared/hooks/useApi';
import { useCrud } from '../../shared/hooks/useCrud';
import inventoryService, { type InventoryItem } from '../../services/inventoryService';
import { 
  Package, 
  Plus, 
  Edit, 
  Trash2, 
  AlertTriangle, 
  DollarSign, 
  TrendingDown, 
  Clock,
  Search,
  Download,
  Upload,
  Loader2,
  AlertCircle 
} from 'lucide-react';

interface StockAlert {
  id: number;
  item_name: string;
  current_stock: number;
  minimum_stock: number;
  alert_type: 'low_stock' | 'expired' | 'expiring_soon';
  days_until_expiry?: number;
}

const InventoryManagement: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'inventory' | 'alerts' | 'orders' | 'reports'>('inventory');
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Use CRUD hook for inventory items
  const {
    items: inventoryItems,
    loading,
    error,
    fetchAll: fetchInventoryItems,
    create: createInventoryItem,
    update: updateInventoryItem,
    remove: deleteInventoryItem,
  } = useCrud(inventoryService, {
    immediate: true,
  });

  // Fetch stock alerts (low stock items)
  const {
    data: lowStockItems,
    loading: alertsLoading,
    error: alertsError,
    execute: fetchStockAlerts,
  } = useApi(() => inventoryService.getLowStockItems(), {
    immediate: true,
  });

  // Transform low stock items to stock alerts format
  const stockAlerts: StockAlert[] = lowStockItems?.map((item: any) => ({
    id: item.id,
    item_name: item.name,
    current_stock: item.current_stock,
    minimum_stock: item.minimum_stock,
    alert_type: 'low_stock' as const,
  })) || [];

  // Show loading state
  if (loading || alertsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-muted-foreground">{t('common.loading')}...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || alertsError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-2">{t('inventory.errorLoadingData')}</p>
            <div className="flex items-center justify-center gap-3">
              <Button onClick={() => fetchInventoryItems()} variant="outline">
                {t('common.retry')}
              </Button>
              <Button onClick={() => fetchStockAlerts()} variant="glass">
                {t('inventory.retryAlerts')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // CRUD Handlers
  const handleCreateItem = () => {
    setSelectedItem(null);
    setModalMode('create');
    setShowModal(true);
  };

  const handleEditItem = (item: InventoryItem) => {
    setSelectedItem(item);
    setModalMode('edit');
    setShowModal(true);
  };

  const handleDeleteItem = async (itemId: number) => {
    if (!confirm(t('inventory.confirmDelete'))) return;
    try {
      await deleteInventoryItem(itemId);
    } catch (error) {
      console.error('Failed to delete item:', error);
      alert(t('inventory.deleteError'));
    }
  };

  const handleSubmitItem = async (itemData: any) => {
    try {
      if (modalMode === 'create') {
        await createInventoryItem(itemData);
      } else if (selectedItem) {
        await updateInventoryItem(selectedItem.id as number, itemData);
      }
      setShowModal(false);
      setSelectedItem(null);
    } catch (error) {
      console.error('Failed to save item:', error);
      throw error;
    }
  };

  // Calculate statistics
  const totalItems = inventoryItems.length;
  const totalValue = inventoryItems.reduce((sum, item) => {
    const stock = typeof item.current_stock === 'number' ? item.current_stock : parseInt(item.current_stock as string) || 0;
    const cost = typeof item.unit_cost === 'number' ? item.unit_cost : parseFloat(item.unit_cost as string) || 0;
    return sum + (stock * cost);
  }, 0);
  const lowStockCount = stockAlerts.length;

  // Filter items based on search
  const filteredItems = inventoryItems.filter((item: InventoryItem) =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category?.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Form fields for inventory modal
  const inventoryFormFields = [
    { key: 'sku', label: 'SKU', type: 'text' as const, required: true },
    { key: 'name', label: 'Item Name', type: 'text' as const, required: true },
    { key: 'description', label: 'Description', type: 'textarea' as const },
    { key: 'category', label: 'Category', type: 'text' as const, required: true },
    { key: 'current_stock', label: 'Current Stock', type: 'number' as const, required: true },
    { key: 'minimum_stock', label: 'Minimum Stock', type: 'number' as const, required: true },
    { key: 'maximum_stock', label: 'Maximum Stock', type: 'number' as const },
    { key: 'unit_cost', label: 'Unit Cost', type: 'number' as const, step: '0.01', required: true },
    { key: 'item_type', label: 'Item Type', type: 'select' as const, options: [
      { value: 'medicine', label: 'Medicine' },
      { value: 'equipment', label: 'Equipment' },
      { value: 'supplies', label: 'Supplies' },
      { value: 'consumable', label: 'Consumable' }
    ]},
    { key: 'supplier', label: 'Supplier', type: 'text' as const },
    { key: 'expiry_date', label: 'Expiry Date', type: 'date' as const },
  ];

  // Table columns for inventory
  const inventoryColumns = [
    { key: 'sku', label: 'SKU', sortable: true },
    { key: 'name', label: 'Name', sortable: true },
    { key: 'category', label: 'Category', sortable: true, render: (item: InventoryItem) => item.category?.name || 'N/A' },
    { key: 'current_stock', label: 'Stock', sortable: true, render: (item: InventoryItem) => (
      <Badge variant={item.current_stock <= item.minimum_stock ? 'destructive' : 'secondary'}>
        {item.current_stock}
      </Badge>
    )},
    { key: 'minimum_stock', label: 'Min Stock', sortable: true },
    { key: 'unit_cost', label: 'Unit Cost', sortable: true, render: (item: InventoryItem) => `$${item.unit_cost}` },
    { key: 'actions', label: 'Actions', render: (item: InventoryItem) => (
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="sm" onClick={() => handleEditItem(item)}>
          <Edit className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="sm" onClick={() => handleDeleteItem(item.id as number)}>
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>
    )},
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'inventory':
        return (
          <div className="space-y-6">
            {/* Search and Actions */}
            <div className="flex items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder={t('inventory.searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  {t('inventory.export')}
                </Button>
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  {t('inventory.import')}
                </Button>
                <Button onClick={handleCreateItem}>
                  <Plus className="w-4 h-4 mr-2" />
                  {t('inventory.addNewItem')}
                </Button>
              </div>
            </div>

            {/* Inventory Table */}
            <DataTable
              data={filteredItems}
              columns={inventoryColumns}
              loading={loading}
              emptyMessage={t('inventory.noItems')}
            />
          </div>
        );

      case 'alerts':
        return (
          <div className="space-y-6">
            <div className="grid gap-4">
              {stockAlerts.map((alert) => (
                <Card key={alert.id} className="border-l-4 border-l-red-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-foreground">{alert.item_name}</h4>
                        <p className="text-sm text-muted-foreground">
                          Current: {alert.current_stock} | Minimum: {alert.minimum_stock}
                        </p>
                      </div>
                      <Badge variant="destructive">
                        {alert.alert_type === 'low_stock' ? t('inventory.lowStock') : t('inventory.expiring')}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
              {stockAlerts.length === 0 && (
                <div className="text-center py-8">
                  <AlertTriangle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">{t('inventory.noAlerts')}</p>
                </div>
              )}
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8">
            <p className="text-muted-foreground">{t('inventory.comingSoon')}</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <Card variant="glass" className="shadow-xl">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Package className="w-6 h-6 text-white" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold macos-text-primary">
                    {t('inventory.title')}
                  </CardTitle>
                  <CardDescription className="macos-text-secondary">
                    {t('inventory.subtitle')}
                  </CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card variant="glass" className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Package className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">
                    {t('inventory.totalItems')}
                  </p>
                  <p className="text-2xl font-bold text-foreground">{totalItems}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card variant="glass" className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <DollarSign className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">
                    {t('inventory.totalValue')}
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    ${totalValue.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card variant="glass" className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <TrendingDown className="w-6 h-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">
                    {t('inventory.lowStockAlerts')}
                  </p>
                  <p className="text-2xl font-bold text-foreground">{lowStockCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card variant="glass" className="shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Clock className="w-6 h-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">
                    {t('inventory.expiringSoon')}
                  </p>
                  <p className="text-2xl font-bold text-foreground">0</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Card variant="glass" className="shadow-lg">
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8 px-6">
              {[
                { id: 'inventory', label: t('inventory.inventory'), icon: '📦' },
                { id: 'alerts', label: t('inventory.alerts'), icon: '⚠️', count: stockAlerts.length },
                { id: 'orders', label: t('inventory.purchaseOrders'), icon: '📋' },
                { id: 'reports', label: t('inventory.reports'), icon: '📊' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
                  }`}
                >
                  <span>{tab.icon}</span>
                  {tab.label}
                  {tab.count !== undefined && tab.count > 0 && (
                    <Badge variant="destructive" className="ml-1">
                      {tab.count}
                    </Badge>
                  )}
                </button>
              ))}
            </nav>
          </div>
          <CardContent className="p-6">
            {renderTabContent()}
          </CardContent>
        </Card>

        {/* CRUD Modal */}
        <CRUDModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          onSubmit={handleSubmitItem}
          title={modalMode === 'create' ? t('inventory.addNewItem') : t('inventory.editItem')}
          fields={inventoryFormFields}
          initialData={selectedItem}
          mode={modalMode}
        />
      </div>
    </div>
  );
};

export default InventoryManagement;
