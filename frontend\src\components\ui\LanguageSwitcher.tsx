import React, {
    useState
  } from 'react';
  import {
    useTranslation
  } from 'react-i18next';
  interface Language {
    code: string;
  name: string;
  nativeName: string;
  flag: string;
  }

const languages: Language[] = [ {
    code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸',
  }, {
    code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦',
  }, ];
  const LanguageSwitcher: React.FC = () => {
    const {
    i18n
  } =
  useTranslation();
  const [isOpen, setIsOpen] =
  useState(false);
  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0];
  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
  setIsOpen(false); // Update document direction for RTL support
  if (languageCode === 'ar') {
    document.documentElement.dir = 'rtl';
  document.documentElement.lang = 'ar';
  } else {
    document.documentElement.dir = 'ltr';
  document.documentElement.lang = 'en';
  }
  };
  return ( <div className="relative"> <button onClick={() => setIsOpen(!isOpen)
  } className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-foreground bg-background border border-border rounded-md hover:bg-muted focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" > <span className="text-lg">{
    currentLanguage.flag
  }</span> <span className="hidden sm:block">{
    currentLanguage.nativeName
  }</span> <svg className={`w-4 h-4 transition-transform ${
    isOpen ? 'rotate-180' : ''
  }`
  } fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2
  } d="M19 9l-7 7-7-7" /> </svg> </button> {
    isOpen && ( <> {/* Backdrop */
  } <div className="fixed inset-0 z-10" onClick={() => setIsOpen(false)
  } /> {/* Dropdown */
  } <div className="absolute right-0 z-20 mt-2 w-48 bg-background border border-border rounded-md shadow-lg"> <div className="py-1"> {
    languages.map((language) => ( <button key={
    language.code
  } onClick={() => handleLanguageChange(language.code)
  } className={`flex items-center w-full px-4 py-2 text-sm text-left hover:bg-muted ${
    currentLanguage.code === language.code ? 'bg-blue-50 text-sky-700 dark:text-sky-400' : 'text-foreground'
  }`
  } > <span className="text-lg mr-3">{
    language.flag
  }</span> <div> <div className="font-medium">{
    language.nativeName
  }</div> <div className="text-xs text-gray-500">{
    language.name
  }</div> </div> {
    currentLanguage.code === language.code && ( <svg className="w-4 h-4 ml-auto text-sky-700 dark:text-sky-400" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /> </svg> )
  } </button> ))
  } </div> </div> </> )
  } </div> );
  };
  export default LanguageSwitcher;
