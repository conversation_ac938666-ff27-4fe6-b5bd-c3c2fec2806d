/** * Common validation utilities for HMS frontend * Consolidates duplicate validation logic across components */ export interface ValidationResult {
    isValid: boolean;
  message?: string;
  } export interface FormValidationResult {
    isValid: boolean;
  errors: Record<string, string>;
  } export class ValidationUtils {
/** * Validate email format */ static validateEmail(email: string): ValidationResult {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailRegex.test(email.trim());
  return {
    isValid, message: isValid ? undefined : 'Please enter a valid email address'
  };
  } /** * Validate phone number format */ static validatePhoneNumber(phone: string): ValidationResult {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15
  }$/;
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  const isValid = phoneRegex.test(cleanPhone);
  return {
    isValid, message: isValid ? undefined : 'Please enter a valid phone number'
  };
  } /** * Validate password strength */ static validatePassword(password: string): ValidationResult {
    if (password.length < 8) {
    return {
    isValid: false, message: 'Password must be at least 8 characters long'
  };
  }
  if (!/(?=.*[a-z])/.test(password)) {
    return {
    isValid: false, message: 'Password must contain at least one lowercase letter'
  };
  }
  if (!/(?=.*[A-Z])/.test(password)) {
    return {
    isValid: false, message: 'Password must contain at least one uppercase letter'
  };
  }
  if (!/(?=.*\d)/.test(password)) {
    return {
    isValid: false, message: 'Password must contain at least one number'
  };
  } return {
    isValid: true
  };
  } /** * Validate username format */ static validateUsername(username: string): ValidationResult {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20
  }$/;
  const isValid = usernameRegex.test(username.trim());
  return {
    isValid, message: isValid ? undefined : 'Username must be 3-20 characters and contain only letters, numbers, and underscores'
  };
  } /** * Validate patient ID format (P000XXX) */ static validatePatientId(patientId: string): ValidationResult {
    const patientIdRegex = /^P\d{6
  }$/;
  const isValid = patientIdRegex.test(patientId.trim());
  return {
    isValid, message: isValid ? undefined : 'Patient ID must be in format P000XXX'
  };
  } /** * Validate required field */ static validateRequired(value: string, fieldName: string = 'Field'): ValidationResult {
    const isValid = value.trim().length > 0;
  return {
    isValid, message: isValid ? undefined : `${
    fieldName
  } is required`
  };
  } /** * Validate name format */ static validateName(name: string): ValidationResult {
    const nameRegex = /^[a-zA-Z\s]{2,50
  }$/;
  const isValid = nameRegex.test(name.trim());
  return {
    isValid, message: isValid ? undefined : 'Name must be 2-50 characters and contain only letters and spaces'
  };
  } /** * Validate date format (YYYY-MM-DD) */ static validateDate(date: string): ValidationResult {
    const dateRegex = /^\d{4
  }-\d{2
  }-\d{2
  }$/;
  if (!dateRegex.test(date)) {
    return {
    isValid: false, message: 'Date must be in YYYY-MM-DD format'
  };
  }

const parsedDate = new Date(date);
  const isValid = parsedDate instanceof Date && !isNaN(parsedDate.getTime());
  return {
    isValid, message: isValid ? undefined : 'Please enter a valid date'
  };
  } /** * Validate time format (HH:MM) */ static validateTime(time: string): ValidationResult {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  const isValid = timeRegex.test(time);
  return {
    isValid, message: isValid ? undefined : 'Time must be in HH:MM format'
  };
  } /** * Validate appointment date (must be in the future) */ static validateAppointmentDate(date: string): ValidationResult {
    const dateValidation = this.validateDate(date);
  if (!dateValidation.isValid) {
    return dateValidation;
  }

const appointmentDate = new Date(date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (appointmentDate < today) {
    return {
    isValid: false, message: 'Appointment date must be in the future'
  };
  } return {
    isValid: true
  };
  } /** * Validate age */ static validateAge(age: number): ValidationResult {
    if (age < 0 || age > 150) {
    return {
    isValid: false, message: 'Please enter a valid age between 0 and 150'
  };
  } return {
    isValid: true
  };
  } /** * Validate form data against rules */ static validateForm(data: Record<string, any>, rules: Record<string, any>): FormValidationResult {
    const errors: Record<string, string> = {
  };
  for (const [field, rule] of Object.entries(rules)) {
    const value = data[field]; // Required validation
  if (rule.required) {
    const requiredValidation = this.validateRequired(value || '', field);
  if (!requiredValidation.isValid) {
    errors[field] = requiredValidation.message!;
  continue;
  }
  } // Skip other validations if field is empty and not required
  if (!value && !rule.required) {
    continue;
  } // Email validation
  if (rule.email) {
    const emailValidation = this.validateEmail(value);
  if (!emailValidation.isValid) {
    errors[field] = emailValidation.message!;
  continue;
  }
  } // Phone validation
  if (rule.phone) {
    const phoneValidation = this.validatePhoneNumber(value);
  if (!phoneValidation.isValid) {
    errors[field] = phoneValidation.message!;
  continue;
  }
  } // Password validation
  if (rule.password) {
    const passwordValidation = this.validatePassword(value);
  if (!passwordValidation.isValid) {
    errors[field] = passwordValidation.message!;
  continue;
  }
  } // Username validation
  if (rule.username) {
    const usernameValidation = this.validateUsername(value);
  if (!usernameValidation.isValid) {
    errors[field] = usernameValidation.message!;
  continue;
  }
  } // Name validation
  if (rule.name) {
    const nameValidation = this.validateName(value);
  if (!nameValidation.isValid) {
    errors[field] = nameValidation.message!;
  continue;
  }
  } // Min length validation
  if (rule.minLength && value.length < rule.minLength) {
    errors[field] = `${
    field
  } must be at least ${
    rule.minLength
  } characters`;
  continue;
  } // Max length validation
  if (rule.maxLength && value.length > rule.maxLength) {
    errors[field] = `${
    field
  } must be no more than ${
    rule.maxLength
  } characters`;
  continue;
  } // Custom validation

function
  if (rule.custom && typeof rule.custom === 'function') {
    const customValidation = rule.custom(value);
  if (!customValidation.isValid) {
    errors[field] = customValidation.message!;
  continue;
  }
  }
  } return {
    isValid: Object.keys(errors).length === 0, errors
  };
  } /** * Validate password confirmation */ static validatePasswordConfirmation(password: string, confirmPassword: string): ValidationResult {
    const isValid = password === confirmPassword;
  return {
    isValid, message: isValid ? undefined : 'Passwords do not match'
  };
  } /** * Sanitize input string */ static sanitizeInput(input: string): string {
    return input.trim().replace(/[<>]/g, '');
  } /** * Validate file upload */ static validateFile(file: File, options: {
    maxSize?: number; // in bytes allowedTypes?: string[];
  } = {
  }): ValidationResult {
    const {
    maxSize = 5 * 1024 * 1024, allowedTypes = []
  } = options; // Default 5MB
  if (file.size > maxSize) {
    return {
    isValid: false, message: `File size must be less than ${
    Math.round(maxSize / 1024 / 1024)
  }MB`
  };
  }
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    return {
    isValid: false, message: `File type must be one of: ${
    allowedTypes.join(', ')
  }`
  };
  } return {
    isValid: true
  };
  }
  }
