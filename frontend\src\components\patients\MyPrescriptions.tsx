import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/badge';
import { useTheme } from '../../hooks/useTheme';
import { useApi } from '../../shared/hooks/useApi';
import patientService from '../../services/patientService';
import { prescriptionService } from '../../services/medicalService';
import { Pill, Calendar, User, Clock, AlertTriangle, Download, RefreshCw, Bell, Info, CheckCircle, Loader2, AlertCircle as AlertIcon } from 'lucide-react';
  const MyPrescriptions: React.FC = () => {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  const [activeTab, setActiveTab] = useState('active');

  // Fetch current patient data
  const {
    data: currentPatient,
    loading: patientLoading,
    error: patientError,
  } = useApi(() => patientService.getCurrentPatient(), {
    immediate: true,
    cache: true,
    cacheKey: 'current-patient-prescriptions',
    cacheTTL: 10 * 60 * 1000, // 10 minutes
  });

  // Fetch patient prescriptions
  const {
    data: allPrescriptions,
    loading: prescriptionsLoading,
    error: prescriptionsError,
  } = useApi(() => currentPatient ? patientService.getPatientPrescriptions(currentPatient.id) : Promise.resolve([]), {
    immediate: !!currentPatient,
    cache: true,
    cacheKey: `patient-prescriptions-${currentPatient?.id}`,
    cacheTTL: 10 * 60 * 1000, // 10 minutes
  });

  // Categorize prescriptions
  const prescriptions = allPrescriptions ? {
    active: allPrescriptions.filter(p => p.status === 'active'),
    completed: allPrescriptions.filter(p => p.status === 'completed')
  } : { active: [], completed: [] };

  // Show loading state
  if (patientLoading || prescriptionsLoading || !currentPatient) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-muted-foreground">{t('common.loading')}...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (patientError || prescriptionsError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-center h-64">
          <div className="text-center">
            <AlertIcon className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-2">{t('patient.errorLoadingPrescriptions')}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              {t('common.retry')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const stats = {
    active: prescriptions.active.filter(p => p.status === 'active').length,
    refillNeeded: prescriptions.active.filter(p => new Date(p.end_date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)).length, // Ending within 7 days
    completed: prescriptions.completed.length,
    totalMedications: prescriptions.active.length + prescriptions.completed.length
  };
  const tabs = [ {
    id: 'active', label: 'Active Prescriptions', count: stats.active
  }, {
    id: 'completed', label: 'Completed', count: stats.completed
  } ];
  const getStatusColor = (status: string) => {
    switch (status) {
    case 'active': return 'status-success ';
  case 'refill_needed': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400';
  case 'completed': return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300';
  case 'expired': return 'status-error ';
  default: return 'bg-muted text-foreground dark:bg-gray-800 dark:text-gray-300';
  }
  };
  const getStatusIcon = (status: string) => {
    switch (status) {
    case 'active': return
    <CheckCircle className="w-4 h-4 text-emerald-700 dark:text-emerald-400" />;
  case 'refill_needed': return
    <AlertTriangle className="w-4 h-4 text-orange-600" />;
  case 'completed': return
    <CheckCircle className="w-4 h-4 text-muted-foreground" />;
  default: return
    <Info className="w-4 h-4 text-muted-foreground" />;
  }
  };
  const renderPrescriptionCard = (prescription: any) => (
    <Card key={
    prescription.id
  } variant="glass" className="hover:glass-hover macos-transition">
    <CardContent className="p-6"> <div className="flex items-start justify-between"> <div className="flex-1"> <div className="flex items-center justify-between mb-4"> <h3 className="text-lg font-semibold macos-text-primary">{
    prescription.medication_name
  }</h3>
    <Badge className={`${
    getStatusColor(prescription.status)
  } rounded-full px-3 py-1 flex items-center gap-1`
  }> {
    getStatusIcon(prescription.status)
  } <span>{
    prescription.status.replace('_', ' ')
  }</span>
    </Badge> </div> <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <User className="w-4 h-4 macos-text-tertiary" /> <span>Prescribed by {
    prescription.doctor?.full_name || 'Unknown Doctor'
  }</span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Calendar className="w-4 h-4 macos-text-tertiary" /> <span>{
    prescription.prescribedDate
  }</span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Pill className="w-4 h-4 macos-text-tertiary" /> <span>{
    prescription.dosage
  }</span> </div> <div className="flex items-center space-x-2 text-sm macos-text-secondary">
    <Clock className="w-4 h-4 macos-text-tertiary" /> <span>{
    new Date(prescription.start_date).toLocaleDateString()
  } - {
    new Date(prescription.end_date).toLocaleDateString()
  }</span> </div> </div> <div className="glass-subtle border /50 /50 rounded-xl p-4 mb-4"> <p className="text-sm text-sky-700 dark:text-sky-400 dark:text-blue-400"> <strong>Instructions:</strong> {
    prescription.instructions
  } </p> <p className="text-sm text-sky-700 dark:text-sky-400 dark:text-blue-400 mt-2"> <strong>Purpose:</strong> {
    prescription.purpose
  } </p> </div> {
    prescription.status === 'active' && prescription.nextDose && ( <div className="glass-subtle border /50 /50 rounded-xl p-4 mb-4 bg-green-50/50 dark:bg-green-900/20"> <p className="text-sm text-emerald-700 dark:text-emerald-400 dark:text-green-400"> <strong>Next Dose:</strong> {
    prescription.nextDose
  } </p> </div> )
  } {
    prescription.status === 'refill_needed' && ( <div className="glass-subtle border border-orange-200/50 dark:border-orange-800/50 rounded-xl p-4 mb-4 bg-orange-50/50 dark:bg-orange-900/20"> <p className="text-sm text-orange-800 dark:text-orange-400">
    <AlertTriangle className="w-4 h-4 inline mr-1" /> <strong>Refill Needed:</strong> Contact your doctor or pharmacy </p> </div> )
  } {
    prescription.sideEffects && prescription.sideEffects.length > 0 && ( <div className="glass-subtle border /50 /50 rounded-xl p-4 mb-4 bg-yellow-50/50 dark:bg-yellow-900/20"> <p className="text-sm text-amber-700 dark:text-amber-400 dark:text-yellow-400"> <strong>Possible Side Effects:</strong> {
    prescription.sideEffects.join(', ')
  } </p> </div> )
  } <div className="flex items-center justify-between text-sm macos-text-secondary"> <span>Quantity: <span className="macos-text-primary font-medium">{
    prescription.quantity
  }</span></span> {
    prescription.refillsLeft !== undefined && ( <span>Refills left: <span className="macos-text-primary font-medium">{
    prescription.refillsLeft
  }</span></span> )
  } </div> </div> <div className="flex flex-col space-y-2 ml-6"> {
    prescription.status === 'active' && ( <>
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Bell className="w-4 h-4" /> Set Reminder
    </Button> {
    prescription.refillsLeft > 0 && (
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <RefreshCw className="w-4 h-4" /> Request Refill
    </Button> )
  } </> )
  } {
    prescription.status === 'refill_needed' && (
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <RefreshCw className="w-4 h-4" /> Request Refill
    </Button> )
  }
    <Button variant="glass" size="sm" className="flex items-center gap-2">
    <Download className="w-4 h-4" /> Download
    </Button> </div> </div>
    </CardContent>
    </Card> );
  const getCurrentPrescriptions = () => {
    switch (activeTab) {
    case 'active': return prescriptions.active;
  case 'completed': return prescriptions.completed;
  default: return prescriptions.active;
  }
  };
  return ( <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 p-6"> <div className="max-w-7xl mx-auto space-y-6"> {/* Header */
  }
    <Card variant="glass">
    <CardHeader> <div className="flex items-center justify-between"> <div className="flex items-center gap-4"> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
    <Pill className="w-6 h-6 text-white" /> </div> <div>
    <CardTitle className="text-2xl font-bold macos-text-primary">My Prescriptions
    </CardTitle> <p className="macos-text-secondary">Manage your medications and prescriptions</p> </div> </div>
    <Button variant="glass" className="flex items-center gap-2">
    <Download className="w-4 h-4" /> Export All
    </Button> </div>
    </CardHeader>
    </Card> {/* Statistics */
  } <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
    <Card variant="glass">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Active Medications</p> <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400 dark:text-green-400">{
    stats.active
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
    <Pill className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card variant="glass">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Refill Needed</p> <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{
    stats.refillNeeded
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
    <AlertTriangle className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card variant="glass">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Completed</p> <p className="text-2xl font-bold macos-text-primary">{
    stats.completed
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-gray-500 to-gray-600 rounded-xl flex items-center justify-center shadow-lg">
    <CheckCircle className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card>
    <Card variant="glass">
    <CardContent className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm font-medium macos-text-secondary">Total Medications</p> <p className="text-2xl font-bold text-sky-700 dark:text-sky-400 dark:text-blue-400">{
    stats.totalMedications
  }</p> </div> <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
    <Pill className="w-6 h-6 text-white" /> </div> </div>
    </CardContent>
    </Card> </div> {/* Refill Alerts */
  } {
    stats.refillNeeded > 0 && (
    <Card variant="glass" className="border-orange-200/50 bg-orange-50/50 dark:bg-orange-900/20">
    <CardContent className="p-4"> <div className="flex items-center space-x-2">
    <AlertTriangle className="w-5 h-5 text-orange-600 dark:text-orange-400" /> <p className="text-orange-800 dark:text-orange-300"> <strong>Attention:</strong> You have {
    stats.refillNeeded
  } medication(s) that need refills. </p> </div>
    </CardContent>
    </Card> )
  } {/* Tabs */
  }
    <Card variant="glass">
    <CardContent className="p-6"> <nav className="flex space-x-2"> {
    tabs.map((tab) => ( <button key={
    tab.id
  } onClick={() => setActiveTab(tab.id)
  } className={`flex items-center px-4 py-2 rounded-xl font-medium text-sm macos-transition ${
    activeTab === tab.id ? 'bg-blue-600 text-white shadow-lg' : 'macos-text-secondary hover:macos-text-primary hover:glass-hover'
  }`
  } > {
    tab.label
  } <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
    activeTab === tab.id ? 'bg-background/20 text-white' : 'bg-muted text-muted-foreground dark:bg-gray-800 dark:text-gray-300'
  }`
  }> {
    tab.count
  } </span> </button> ))
  } </nav>
    </CardContent>
    </Card> {/* Prescriptions List */
  } <div className="space-y-4"> {
    getCurrentPrescriptions().length > 0 ? ( getCurrentPrescriptions().map(renderPrescriptionCard) ) : (
    <Card variant="glass">
    <CardContent className="p-12 text-center"> <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
    <Pill className="w-8 h-8 text-white" /> </div> <h3 className="text-lg font-semibold macos-text-primary mb-2"> No {
    activeTab
  } prescriptions </h3> <p className="macos-text-secondary"> {
    activeTab === 'active' ? "You don't have any active prescriptions." : "No completed prescriptions found."
  } </p>
    </CardContent>
    </Card> )
  } </div> </div> </div> );
  };
  export default MyPrescriptions;
