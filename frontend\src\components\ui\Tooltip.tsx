import React, {
    useState, useRef, useEffect
  } from 'react';
  import {
    createPortal
  } from 'react-dom';
  import {
    cn
  } from '../../lib/utils';
  interface TooltipProps {
    content: React.ReactNode;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  disabled?: boolean;
  className?: string;
  }

const Tooltip: React.FC
    <TooltipProps> = ({
    content, children, position = 'top', delay = 500, disabled = false, className,
  }) => {
    const [isVisible, setIsVisible] =
  useState(false);
  const [tooltipPosition, setTooltipPosition] =
  useState({
    x: 0, y: 0
  });
  const triggerRef = useRef
    <HTMLDivElement>(null);
  const tooltipRef = useRef
    <HTMLDivElement>(null);
  const timeoutRef = useRef
    <NodeJS.Timeout>();
  const showTooltip = () => {
    if (disabled) return;
  timeoutRef.current = setTimeout(() => {
    setIsVisible(true);
  updatePosition();
  }, delay);
  };
  const hideTooltip = () => {
    if (timeoutRef.current) {
    clearTimeout(timeoutRef.current);
  } setIsVisible(false);
  };
  const updatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return;
  const triggerRect = triggerRef.current.getBoundingClientRect();
  const tooltipRect = tooltipRef.current.getBoundingClientRect();
  const viewport = {
    width: window.innerWidth, height: window.innerHeight,
  };
  let x = 0;
  let y = 0;
  switch (position) {
    case 'top': x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
  y = triggerRect.top - tooltipRect.height - 8;
  break;
  case 'bottom': x = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
  y = triggerRect.bottom + 8;
  break;
  case 'left': x = triggerRect.left - tooltipRect.width - 8;
  y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
  break;
  case 'right': x = triggerRect.right + 8;
  y = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
  break;
  } // Keep tooltip within viewport x = Math.max(8, Math.min(x, viewport.width - tooltipRect.width - 8));
  y = Math.max(8, Math.min(y, viewport.height - tooltipRect.height - 8));
  setTooltipPosition({
    x, y
  });
  };
  useEffect(() => {
    if (isVisible) {
    updatePosition();
  window.addEventListener('scroll', updatePosition);
  window.addEventListener('resize', updatePosition);
  }
  return () => {
    window.removeEventListener('scroll', updatePosition);
  window.removeEventListener('resize', updatePosition);
  };
  }, [isVisible]);
  const tooltipContent = isVisible && ( <div ref={
    tooltipRef
  } className={
    cn( 'fixed z-50 macos-tooltip animate-in fade-in zoom-in-95 duration-200', className )
  } style={{
    left: tooltipPosition.x, top: tooltipPosition.y,
  }
  } > {
    content
  } </div> );
  return ( <> <div ref={
    triggerRef
  } onMouseEnter={
    showTooltip
  } onMouseLeave={
    hideTooltip
  } onFocus={
    showTooltip
  } onBlur={
    hideTooltip
  } className="inline-block" > {
    children
  } </div> {
    tooltipContent && createPortal(tooltipContent, document.body)
  } </> );
  }; // Simple tooltip hook for programmatic usage export

const useTooltip = () => {
    const [isVisible, setIsVisible] =
  useState(false);
  const [content, setContent] = useState
    <React.ReactNode>('');
  const [position, setPosition] =
  useState({
    x: 0, y: 0
  });
  const show = (content: React.ReactNode, x: number, y: number) => {
    setContent(content);
  setPosition({
    x, y
  });
  setIsVisible(true);
  };
  const hide = () => {
    setIsVisible(false);
  };
  const TooltipComponent = () => {
    if (!isVisible) return null;
  return createPortal( <div className="fixed z-50 macos-tooltip animate-in fade-in zoom-in-95 duration-200" style={{
    left: position.x, top: position.y,
  }
  } > {
    content
  } </div>, document.body );
  };
  return {
    show, hide, TooltipComponent, isVisible,
  };
  };
  export {
    Tooltip
  };
