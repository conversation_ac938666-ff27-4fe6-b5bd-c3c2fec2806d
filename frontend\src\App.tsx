import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { store } from './store';
import { ThemeProvider } from './contexts/ThemeContext';
import SimpleLandingPage from './components/landing/SimpleLandingPage';
import LoginForm from './components/auth/LoginForm';
import Dashboard from './components/dashboard/Dashboard';
import ProtectedRoute from './components/auth/ProtectedRoute';
import RestrictedAccess from './components/auth/RestrictedAccess';
import StyleDemo from './pages/StyleDemo';
import ThemeTestPage from './components/test/ThemeTestPage';
import StyleSystemDemo from './components/test/StyleSystemDemo';
import './i18n/index';
import './styles/rtl.css';
import './utils/healthCheck';

const AppContent: React.FC = () => {
  const { i18n } = useTranslation();

  useEffect(() => {
    // Set initial direction based on language
    const currentLang = i18n.language || 'en';
    if (currentLang === 'ar') {
      document.documentElement.dir = 'rtl';
      document.documentElement.lang = 'ar';
    } else {
      document.documentElement.dir = 'ltr';
      document.documentElement.lang = 'en';
    }
  }, [i18n.language]);

  return (
    <Router>
      <div className="App min-h-screen bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 text-foreground transition-colors duration-300">
        <Routes>
          <Route path="/" element={<SimpleLandingPage />} />
          <Route path="/login" element={<LoginForm />} />
          <Route path="/register" element={<RestrictedAccess />} />
          <Route path="/style-demo" element={<StyleDemo />} />
          <Route path="/theme-test" element={<ThemeTestPage />} />
          <Route path="/style-system-demo" element={<StyleSystemDemo />} />
          <Route path="/style-demo.html" element={<div>Redirecting...</div>} />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
        </Routes>
      </div>
    </Router>
  );
};

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </Provider>
  );
}

export default App;