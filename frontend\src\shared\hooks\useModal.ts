/** * Modal Management Hook * Provides comprehensive modal state management with accessibility and keyboard handling */ import {
    useState, useCallback, useEffect, useRef
  } from 'react';
  export interface ModalState {
    isOpen: boolean;
  data?: any;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  variant?: 'default' | 'danger' | 'success' | 'warning';
  } export interface UseModalOptions {
    closeOnEscape?: boolean;
  closeOnOverlayClick?: boolean;
  preventScroll?: boolean;
  focusOnOpen?: boolean;
  restoreFocus?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  } export interface UseModalResult {
    isOpen: boolean;
  data: any;
  title: string | undefined;
  size: ModalState['size'];
  variant: ModalState['variant'];
  open: (options?: Partial
    <ModalState>) => void;
  close: () => void;
  toggle: () => void;
  setData: (data: any) => void;
  setTitle: (title: string) => void;
  setSize: (size: ModalState['size']) => void;
  setVariant: (variant: ModalState['variant']) => void;
  } /** * Hook for managing modal state with accessibility features */ export

function
  useModal(options: UseModalOptions = {
  }): UseModalResult {
    const {
    closeOnEscape = true, closeOnOverlayClick = true, preventScroll = true, focusOnOpen = true, restoreFocus = true, onOpen, onClose,
  } = options;
  const [state, setState] = useState
    <ModalState>({
    isOpen: false, data: null, title: undefined, size: 'md', variant: 'default',
  });
  const previousActiveElement = useRef
    <HTMLElement | null>(null); // Open modal

const open =
  useCallback((modalOptions: Partial
    <ModalState> = {
  }) => { // Store currently focused element for restoration
  if (restoreFocus) {
    previousActiveElement.current = document.activeElement as HTMLElement;
  } setState(prev => ({ ...prev, isOpen: true, ...modalOptions,
  }));
  onOpen?.();
  }, [onOpen, restoreFocus]); // Close modal

const close =
  useCallback(() => {
    setState(prev => ({ ...prev, isOpen: false,
  })); // Restore focus to previously focused element
  if (restoreFocus && previousActiveElement.current) {
    previousActiveElement.current.focus();
  previousActiveElement.current = null;
  } onClose?.();
  }, [onClose, restoreFocus]); // Toggle modal

const toggle =
  useCallback(() => {
    if (state.isOpen) {
    close();
  } else {
    open();
  }
  }, [state.isOpen, open, close]); // Set modal data

const setData =
  useCallback((data: any) => {
    setState(prev => ({ ...prev, data
  }));
  }, []); // Set modal title

const setTitle =
  useCallback((title: string) => {
    setState(prev => ({ ...prev, title
  }));
  }, []); // Set modal size

const setSize =
  useCallback((size: ModalState['size']) => {
    setState(prev => ({ ...prev, size
  }));
  }, []); // Set modal variant

const setVariant =
  useCallback((variant: ModalState['variant']) => {
    setState(prev => ({ ...prev, variant
  }));
  }, []); // Handle escape key
  useEffect(() => {
    if (!closeOnEscape || !state.isOpen) return;
  const handleEscape = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
    close();
  }
  };
  document.addEventListener('keydown', handleEscape);
  return () => document.removeEventListener('keydown', handleEscape);
  }, [closeOnEscape, state.isOpen, close]); // Handle body scroll prevention
  useEffect(() => {
    if (!preventScroll || !state.isOpen) return;
  const originalStyle = window.getComputedStyle(document.body).overflow;
  document.body.style.overflow = 'hidden';
  return () => {
    document.body.style.overflow = originalStyle;
  };
  }, [preventScroll, state.isOpen]); // Handle focus management
  useEffect(() => {
    if (!focusOnOpen || !state.isOpen) return; // Focus the modal container after a brief delay to ensure it's rendered

const timer = setTimeout(() => {
    const modalElement = document.querySelector('[role="dialog"]') as HTMLElement;
  if (modalElement) {
    modalElement.focus();
  }
  }, 100);
  return () => clearTimeout(timer);
  }, [focusOnOpen, state.isOpen]);
  return {
    isOpen: state.isOpen, data: state.data, title: state.title, size: state.size, variant: state.variant, open, close, toggle, setData, setTitle, setSize, setVariant,
  };
  } /** * Hook for managing multiple modals */ export

function
  useModalStack() {
    const [modals, setModals] = useState
    <Array<{
    id: string;
  modal: UseModalResult
  }>>([]);
  const createModal =
  useCallback((id: string, options?: UseModalOptions) => {
    const modal =
  useModal(options);
  setModals(prev => { // Remove existing modal with same id

const filtered = prev.filter(m => m.id !== id);
  return [...filtered, {
    id, modal
  }];
  });
  return modal;
  }, []);
  const getModal =
  useCallback((id: string) => {
    return modals.find(m => m.id === id)?.modal;
  }, [modals]);
  const removeModal =
  useCallback((id: string) => {
    setModals(prev => prev.filter(m => m.id !== id));
  }, []);
  const closeAll =
  useCallback(() => {
    modals.forEach(({
    modal
  }) => modal.close());
  }, [modals]);
  const getOpenModals =
  useCallback(() => {
    return modals.filter(({
    modal
  }) => modal.isOpen);
  }, [modals]);
  return {
    createModal, getModal, removeModal, closeAll, getOpenModals, modalCount: modals.length, openModalCount: getOpenModals().length,
  };
  } /** * Hook for confirmation modals */ export

function
  useConfirmModal() {
    const modal =
  useModal();
  const [resolvePromise, setResolvePromise] = useState<((value: boolean) => void) | null>(null);
  const confirm =
  useCallback((options: {
    title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  variant?: ModalState['variant'];
  } = {
  }): Promise<boolean> => {
    return new Promise((resolve) => {
    setResolvePromise(() => resolve);
  modal.open({
    title: options.title || 'Confirm Action', data: {
    message: options.message || 'Are you sure you want to proceed?', confirmText: options.confirmText || 'Confirm', cancelText: options.cancelText || 'Cancel',
  }, variant: options.variant || 'default',
  });
  });
  }, [modal]);
  const handleConfirm =
  useCallback(() => {
    resolvePromise?.(true);
  setResolvePromise(null);
  modal.close();
  }, [resolvePromise, modal]);
  const handleCancel =
  useCallback(() => {
    resolvePromise?.(false);
  setResolvePromise(null);
  modal.close();
  }, [resolvePromise, modal]);
  return { ...modal, confirm, handleConfirm, handleCancel,
  };
  } /** * Hook for form modals */ export

function useFormModal
    <T = any>() {
    const modal =
  useModal();
  const [formData, setFormData] = useState
    <T | null>(null);
  const [isSubmitting, setIsSubmitting] =
  useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const openForm =
  useCallback((data?: T, title?: string) => {
    setFormData(data || null);
  setSubmitError(null);
  modal.open({
    title, data
  });
  }, [modal]);
  const closeForm =
  useCallback(() => {
    setFormData(null);
  setSubmitError(null);
  setIsSubmitting(false);
  modal.close();
  }, [modal]);
  const submitForm =
  useCallback(async ( submitHandler: (data: T) => Promise<void>, data: T ) => {
    setIsSubmitting(true);
  setSubmitError(null);
  try {
    await submitHandler(data);
  closeForm();
  } catch (error) {
    setSubmitError(error instanceof Error ? error.message : 'Submission failed');
  } finally {
    setIsSubmitting(false);
  }
  }, [closeForm]);
  return { ...modal, formData, isSubmitting, submitError, openForm, closeForm, submitForm, setFormData,
  };
  }
